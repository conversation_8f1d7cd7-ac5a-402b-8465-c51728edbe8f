// Type definitions for metadata structure (matches backend API exactly)

// Metadata Response DTO (matches UnifiedMetadataResponseDto from backend API)
// Includes ALL fields from both Metadata.cs and DataType.cs entities
export interface Metadata {
  // Core Identity (from Metadata.cs and DataType.cs)
  id?: string;
  name?: string;
  dataTypeId?: string; // Reference to DataType entity

  // DataType Core Properties (from DataType.cs)
  dataTypeName?: string; // DataType.Name
  displayName?: string; // DataType.DisplayName - UI display name for data type
  category?: string; // DataType.Category - primitive, formatted, choice, media, temporal, complex, interactive
  uiComponent?: string; // DataType.UiComponent - UI component to render

  // UI Display Properties (from both entities)
  displayLabel?: string; // Field display label (Metadata overrides DataType)
  helpText?: string; // Field help text (Metadata overrides DataType)
  fieldOrder?: number; // Field display order (Metadata overrides DataType)
  isVisible?: boolean; // Field visibility (Metadata overrides DataType)
  isReadonly?: boolean; // Field readonly state (Metadata overrides DataType)

  // Validation Properties (Metadata overrides DataType)
  validationPattern?: string; // Regex validation pattern
  minLength?: number; // Minimum text length
  maxLength?: number; // Maximum text length
  minValue?: number; // Minimum numeric value
  maxValue?: number; // Maximum numeric value
  isRequired?: boolean; // Required field flag
  decimalPlaces?: number; // Number of decimal places for numeric fields
  stepValue?: number; // Step value for numeric inputs

  // Input Properties (Metadata overrides DataType)
  placeholder?: string; // Input placeholder text
  inputType?: string; // HTML input type (text, number, email, etc.)
  inputMask?: string; // Input mask pattern for formatting
  htmlAttributes?: string; // Additional HTML attributes as JSON

  // Choice/Selection Properties (Metadata overrides DataType)
  defaultOptions?: string; // Default options for dropdowns/multi-select (JSON or comma-separated)
  allowsMultiple?: boolean; // Allow multiple selections
  allowsCustomOptions?: boolean; // Allow custom user-defined options
  maxSelections?: number; // Maximum number of selections for multi-select

  // File/Media Properties (Metadata overrides DataType)
  allowedFileTypes?: string; // Allowed file types (comma-separated)
  maxFileSizeBytes?: number; // Maximum file size in bytes

  // Error Messages (Metadata overrides DataType)
  errorMessage?: string; // General error message
  requiredErrorMessage?: string; // Required field error message
  patternErrorMessage?: string; // Pattern validation error message
  minLengthErrorMessage?: string; // Minimum length error message
  maxLengthErrorMessage?: string; // Maximum length error message
  minValueErrorMessage?: string; // Minimum value error message
  maxValueErrorMessage?: string; // Maximum value error message
  fileTypeErrorMessage?: string; // File type validation error message
  fileSizeErrorMessage?: string; // File size validation error message (from DataType.cs)

  // Link Information (from ObjectMetadata entity)
  metadataLinkId?: string; // Link to ObjectMetadata
  isUnique?: boolean; // Unique constraint flag
  isVisibleInList?: boolean; // Show in list views
  isVisibleInEdit?: boolean; // Show in edit forms
  isVisibleInCreate?: boolean; // Show in create forms
  isVisibleInView?: boolean; // Show in view/detail forms
  isCalculated?: boolean; // Calculated field flag

  // Override References (from Metadata.cs)
  contextId?: string; // Override master Context ID
  tenantContextId?: string; // Override TenantContext ID
  objectLookupId?: string; // Override ObjectLookup ID

  // Lookup Overrides (from Metadata.cs)
  overrideLookupType?: string; // Override lookup type (master, tenant, object)
  overrideMasterContextId?: string; // Override master Context ID
  overrideTenantContextId?: string; // Override TenantContext ID
  overrideObjectLookupId?: string; // Override ObjectLookup ID
}

// Value information for metadata (matches ValueInfoDto from backend)
export interface ValueInfo {
  id: string;
  refId: string;
  value?: string;
  parentObjectValueId?: string;
}

// Metadata with Values (matches UnifiedMetadataWithValuesResponseDto from backend)
export interface MetadataWithValues {
  metadata: Metadata;
  values: ValueInfo[];
}

// Object hierarchical structure (matches UnifiedObjectHierarchicalDto from backend)
export interface ObjectHierarchical {
  id: string;
  name: string;
  description?: string;
  parentObjectId?: string | null;
  isActive: boolean;
  icon?: string;
  createdAt: string;
  createdBy: string;
  modifiedAt?: string;
  modifiedBy?: string;
  isDeleted: boolean;
  hierarchyLevel: number;
  hierarchyPath: string;
  metadata: MetadataWithValues[];
  childObjects: ObjectHierarchical[];
}

// Product hierarchical structure (matches UnifiedProductHierarchicalDto from backend)
export interface ProductHierarchical {
  id: string;
  name: string;
  description?: string;
  version?: string;
  isActive: boolean;
  isUserImported: boolean;
  isRoleAssigned: boolean;
  apiKey?: string;
  isOnboardCompleted: boolean;
  applicationUrl?: string;
  icon?: string;
  createdAt: string;
  createdBy: string;
  modifiedAt?: string;
  modifiedBy?: string;
  isDeleted: boolean;
  metadata: MetadataWithValues[];
  rootObjects: ObjectHierarchical[];
}

// Hierarchical Entity Data Response (matches UnifiedHierarchicalEntityDataResponseDto from backend)
export interface HierarchicalEntityDataResponse {
  products: ProductHierarchical[];
  totalObjectsCount: number;
  totalMetadataCount: number;
}

// API Response wrapper (matches Result<T> from backend)
export interface MetadataResponse {
  succeeded: boolean;
  data: HierarchicalEntityDataResponse;
  message: string | null;
  statusCode?: number;
}

// Navigation-specific types derived from unified metadata
export interface NavigationNode {
  id?: string;
  name?: string;
  description?: string;
  path?: string;
  icon?: string;
  hierarchyLevel?: number;
  hierarchyPath?: string;
  parentObjectId?: string | null;
  children?: NavigationNode[];
  isActive?: boolean;
  isDeleted?: boolean;
  type?: 'product' | 'object';
  metadata?: MetadataWithValues[]; // Updated to use metadata
  version?: string;
  rootObjects?: NavigationNode[];
  // Additional properties from unified structure
  createdAt?: string;
  createdBy?: string;
  modifiedAt?: string;
  modifiedBy?: string;
  isUserImported?: boolean;
  isRoleAssigned?: boolean;
  apiKey?: string;
  isOnboardCompleted?: boolean;
  applicationUrl?: string;
}

export interface NavigationData {
  nodes: NavigationNode[];
  lastUpdated: Date;
  isLoading: boolean;
  error: string | null;
}

// Legacy interfaces for backward compatibility (deprecated)
export interface MetadataObject extends ObjectHierarchical {}
export interface Product extends ProductHierarchical {}
