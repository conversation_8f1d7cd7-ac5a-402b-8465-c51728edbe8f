import React, { useState, useEffect } from 'react';
import { Form, Spinner, Dropdown, InputGroup } from 'react-bootstrap';
import { Search } from 'lucide-react';
import { primaryClient } from '../../services/httpClient';

interface Tenant {
  id: string;
  name: string;
  adminEmail: string;
}

interface TenantDropdownProps {
  className?: string;
  disabled?: boolean;
  required?: boolean;
  value?: string;
  onChange?: (tenantId: string | null) => void;
}

export const TenantDropdown: React.FC<TenantDropdownProps> = ({
  className = '',
  disabled = false,
  required = false,
  value,
  onChange
}) => {
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTenant, setSelectedTenant] = useState<Tenant | null>(null);
  const [showDropdown, setShowDropdown] = useState(false);

  // Load tenants from API
  useEffect(() => {
    loadTenants();
  }, []);

  // Update selected tenant when value prop changes
  useEffect(() => {
    if (value && tenants.length > 0) {
      const tenant = tenants.find(t => t.id === value || t.name === value);
      setSelectedTenant(tenant || null);
    } else {
      setSelectedTenant(null);
    }
  }, [value, tenants]);

  const loadTenants = async () => {
    try {
      setLoading(true);
      // Use the base API URL to load tenants
      const response = await primaryClient.get('/tenants');
      setTenants(response.data || []);
    } catch (error) {
      console.error('Error loading tenants:', error);
      setTenants([]);
    } finally {
      setLoading(false);
    }
  };

  const filteredTenants = tenants.filter(tenant =>
    tenant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    tenant.adminEmail.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleTenantSelect = (tenant: Tenant) => {
    setSelectedTenant(tenant);
    setShowDropdown(false);
    setSearchTerm('');
    if (onChange) {
      onChange(tenant.id);
    }
  };

  const handleClear = () => {
    setSelectedTenant(null);
    setSearchTerm('');
    if (onChange) {
      onChange(null);
    }
  };

  return (
    <div className={`tenant-dropdown ${className}`} style={{ position: 'relative' }}>
      <InputGroup>
        <Form.Control
          type="text"
          placeholder={loading ? 'Loading tenants...' : 'Search and select a tenant...'}
          value={selectedTenant ? `${selectedTenant.name} (${selectedTenant.adminEmail})` : searchTerm}
          onChange={(e) => {
            if (!selectedTenant) {
              setSearchTerm(e.target.value);
              setShowDropdown(true);
            }
          }}
          onFocus={() => {
            if (!selectedTenant) {
              setShowDropdown(true);
            }
          }}
          disabled={disabled || loading}
          required={required}
          readOnly={!!selectedTenant}
        />
        <InputGroup.Text>
          {loading ? (
            <Spinner animation="border" size="sm" />
          ) : (
            <Search size={16} />
          )}
        </InputGroup.Text>
        {selectedTenant && (
          <InputGroup.Text
            style={{ cursor: 'pointer' }}
            onClick={handleClear}
            title="Clear selection"
          >
            ×
          </InputGroup.Text>
        )}
      </InputGroup>

      {/* Dropdown Menu */}
      {showDropdown && !selectedTenant && !loading && (
        <div
          className="dropdown-menu show w-100 mt-1"
          style={{
            position: 'absolute',
            zIndex: 1050,
            maxHeight: '200px',
            overflowY: 'auto'
          }}
        >
          {filteredTenants.length === 0 ? (
            <div className="dropdown-item-text text-muted">
              {searchTerm ? 'No tenants match your search' : 'No tenants available'}
            </div>
          ) : (
            filteredTenants.map((tenant) => (
              <button
                key={tenant.id}
                className="dropdown-item"
                onClick={() => handleTenantSelect(tenant)}
                type="button"
              >
                <div>
                  <strong>{tenant.name}</strong>
                  <br />
                  <small className="text-muted">{tenant.adminEmail}</small>
                </div>
              </button>
            ))
          )}
        </div>
      )}

      {/* Click outside to close dropdown */}
      {showDropdown && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 1040
          }}
          onClick={() => setShowDropdown(false)}
        />
      )}
    </div>
  );
};
