/* Custom AG-Grid theme integration */

/* Override AG-Grid theme variables to match our design system */
.ag-theme-alpine {
  --ag-background-color: rgb(var(--color-background));
  --ag-foreground-color: rgb(var(--color-foreground));
  --ag-border-color: rgb(var(--color-border));
  --ag-secondary-border-color: rgb(var(--color-border));
  --ag-header-background-color: rgb(var(--color-primary-500));
  --ag-header-foreground-color: rgb(var(--color-surface-50));
  --ag-odd-row-background-color: rgb(var(--color-background));
  --ag-even-row-background-color: rgba(var(--color-muted), 0.3);
  --ag-row-hover-color: rgba(var(--color-muted), 0.5);
  --ag-selected-row-background-color: rgba(var(--color-primary-500), 0.1);
  --ag-range-selection-background-color: rgba(var(--color-primary-500), 0.2);
  --ag-range-selection-border-color: rgb(var(--color-primary-500));
  --ag-header-cell-hover-background-color: rgba(var(--color-muted), 0.7);
  --ag-header-cell-moving-background-color: rgb(var(--color-muted));
  --ag-value-change-value-highlight-background-color: rgba(var(--color-primary-500), 0.2);
  --ag-chip-background-color: rgb(var(--color-primary-500));
  --ag-chip-text-color: rgb(var(--color-surface-50));

  /* Font settings */
  --ag-font-family: inherit;
  --ag-font-size: 14px;

  /* Spacing */
  --ag-grid-size: 4px;
  --ag-cell-horizontal-padding: 12px;
  --ag-header-height: 40px;
  --ag-row-height: 36px;

  /* Borders */
  --ag-borders: solid 1px;
  --ag-border-radius: 6px;
}

/* Custom header styling - Lighter Font Weights */
.ag-theme-alpine .ag-header {
  background-color: rgb(var(--color-primary-500));
  border-bottom: 2px solid rgb(var(--color-border));
  font-weight: 500;
}

.ag-theme-alpine .ag-header-cell {
  background-color: rgb(var(--color-primary-500));
  border-right: 1px solid rgba(var(--color-surface-50), 0.2);
}

.ag-theme-alpine .ag-header-cell-label {
  font-weight: 500;
  color: rgb(var(--color-surface-50));
  /* Prevent header text truncation */
  white-space: nowrap;
  overflow: visible;
  text-overflow: unset;
}

/* Ensure header text is fully visible */
.ag-theme-alpine .ag-header-cell-text {
  white-space: nowrap;
  overflow: visible;
  text-overflow: unset;
  width: auto;
  min-width: fit-content;
  color: rgb(var(--color-surface-50));
}

/* Auto-size columns to fit header content */
.ag-theme-alpine .ag-header-cell {
  min-width: fit-content;
  width: auto;
}

/* Row styling */
.ag-theme-alpine .ag-row {
  border-bottom: 1px solid rgba(var(--color-border), 0.5);
}

.ag-theme-alpine .ag-row:hover {
  background-color: rgba(var(--color-muted), 0.5);
}

.ag-theme-alpine .ag-row-selected {
  background-color: rgba(var(--color-primary-500), 0.1);
  border-color: rgba(var(--color-primary-500), 0.3);
}

/* Cell styling */
.ag-theme-alpine .ag-cell {
  border-right: 1px solid rgba(var(--color-border), 0.3);
  display: flex;
  align-items: center;
}

.ag-theme-alpine .ag-cell-focus {
  border: 2px solid rgb(var(--color-primary-500));
}

/* Pagination styling */
.ag-theme-alpine .ag-paging-panel {
  border-top: 2px solid rgb(var(--color-border));
  background-color: rgba(var(--color-muted), 0.3);
  color: rgb(var(--color-foreground));
}

.ag-theme-alpine .ag-paging-button {
  color: rgb(var(--color-foreground));
  border: 1px solid rgb(var(--color-border));
  background-color: rgb(var(--color-background));
}

.ag-theme-alpine .ag-paging-button:hover {
  background-color: rgb(var(--color-muted));
}

.ag-theme-alpine .ag-paging-button[disabled] {
  color: rgb(var(--color-muted-foreground));
  background-color: rgba(var(--color-muted), 0.5);
}

/* Filter styling */
.ag-theme-alpine .ag-filter-toolpanel-header {
  background-color: rgb(var(--color-muted));
  color: rgb(var(--color-foreground));
  font-weight: 600;
}

.ag-theme-alpine .ag-filter-toolpanel-search {
  border: 1px solid rgb(var(--color-border));
  background-color: rgb(var(--color-background));
  color: rgb(var(--color-foreground));
}

/* Menu styling */
.ag-theme-alpine .ag-menu {
  background-color: rgb(var(--color-background));
  border: 1px solid rgb(var(--color-border));
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

.ag-theme-alpine .ag-menu-option-active {
  background-color: rgba(var(--color-primary-500), 0.1);
  color: rgb(var(--color-primary-500));
}

/* Loading overlay */
.ag-theme-alpine .ag-overlay-loading-wrapper {
  background-color: rgba(var(--color-background), 0.9);
}

.ag-theme-alpine .ag-overlay-loading-center {
  background-color: rgb(var(--color-background));
  border: 1px solid rgb(var(--color-border));
  border-radius: var(--ag-border-radius);
  padding: 20px;
  color: rgb(var(--color-foreground));
}

/* Scrollbar styling */
.ag-theme-alpine .ag-body-horizontal-scroll::-webkit-scrollbar,
.ag-theme-alpine .ag-body-vertical-scroll::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.ag-theme-alpine .ag-body-horizontal-scroll::-webkit-scrollbar-track,
.ag-theme-alpine .ag-body-vertical-scroll::-webkit-scrollbar-track {
  background: rgba(var(--color-muted), 0.3);
}

.ag-theme-alpine .ag-body-horizontal-scroll::-webkit-scrollbar-thumb,
.ag-theme-alpine .ag-body-vertical-scroll::-webkit-scrollbar-thumb {
  background: rgba(var(--color-muted-foreground), 0.5);
  border-radius: 4px;
}

.ag-theme-alpine .ag-body-horizontal-scroll::-webkit-scrollbar-thumb:hover,
.ag-theme-alpine .ag-body-vertical-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--color-muted-foreground), 0.7);
}

/* Custom cell renderers */
.ag-cell-status-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 9999px;
  font-size: 12px;
  font-weight: 500;
}

.ag-cell-tag {
  display: inline-flex;
  align-items: center;
  padding: 1px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 400;
  margin: 1px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ag-theme-alpine {
    --ag-cell-horizontal-padding: 8px;
    --ag-font-size: 13px;
    --ag-row-height: 32px;
    --ag-header-height: 36px;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .ag-theme-alpine {
    --ag-odd-row-background-color: rgb(var(--color-background));
    --ag-even-row-background-color: rgba(var(--color-muted), 0.2);
  }
}

/* Grid container styling */
.ag-grid-container {
  min-height: 250px;
  height: 100%;
  width: 100%;
  border: none;
  outline: none;
}

/* Responsive grid classes */
.ag-grid-mobile {
  --ag-cell-horizontal-padding: 6px;
  --ag-font-size: 12px;
  --ag-row-height: 30px;
  --ag-header-height: 34px;
}

.ag-grid-tablet {
  --ag-cell-horizontal-padding: 8px;
  --ag-font-size: 13px;
  --ag-row-height: 32px;
  --ag-header-height: 36px;
}

.ag-grid-desktop {
  --ag-cell-horizontal-padding: 12px;
  --ag-font-size: 14px;
  --ag-row-height: 36px;
  --ag-header-height: 40px;
}

/* Mobile-specific column adjustments */
.ag-grid-mobile .ag-header-cell,
.ag-grid-mobile .ag-cell {
  min-width: 80px;
}

/* Hide less important columns on mobile */
@media (max-width: 767px) {
  .ag-grid-mobile .ag-header-cell[col-id="lastModifiedOn"],
  .ag-grid-mobile .ag-cell[col-id="lastModifiedOn"],
  .ag-grid-mobile .ag-header-cell[col-id="createdOn"],
  .ag-grid-mobile .ag-cell[col-id="createdOn"],
  .ag-grid-mobile .ag-header-cell[col-id="phoneNumber"],
  .ag-grid-mobile .ag-cell[col-id="phoneNumber"] {
    display: none;
  }
}

/* Remove any border outline from the ag-grid root */
.ag-theme-alpine {
  border: none;
  outline: none;
}
