using Abstraction.Database.Repositories;
using Application.ComprehensiveEntityData.DTOs;
using Domain.MultiTenancy;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Npgsql;
using System.Data;
using Dapper;
using Finbuckle.MultiTenant;
using Mapster;

namespace Infrastructure.Database.Repositories;

/// <summary>
/// Repository for comprehensive subscription data across all tenants
/// </summary>
public class ComprehensiveSubscriptionRepository : IComprehensiveSubscriptionRepository
{
    private readonly string _connectionString;
    private readonly ILogger<ComprehensiveSubscriptionRepository> _logger;

    public ComprehensiveSubscriptionRepository(
        IOptions<DatabaseSettings> options,
        ILogger<ComprehensiveSubscriptionRepository> logger)
    {
        _connectionString = options.Value.ConnectionString;
        _logger = logger;
    }

    /// <summary>
    /// Create a new database connection
    /// </summary>
    private IDbConnection CreateConnection() => new NpgsqlConnection(_connectionString);

    /// <summary>
    /// Get comprehensive subscription data across all tenants with filtering and pagination
    /// </summary>
    public async Task<object> GetComprehensiveSubscriptionsAsync(
        string? tenantId = null,
        Guid? productId = null,
        string? status = null,
        string? subscriptionType = null,
        bool? isActive = null,
        bool? isExpired = null,
        string? pricingTier = null,
        string? searchTerm = null,
        DateTime? startDateFrom = null,
        DateTime? startDateTo = null,
        DateTime? endDateFrom = null,
        DateTime? endDateTo = null,
        int? expiringWithinDays = null,
        int pageNumber = 1,
        int pageSize = 50,
        string orderBy = "CreatedAt",
        string orderDirection = "desc",
        bool includeSummary = true,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Fetching comprehensive subscription data across all tenants");

            // Validate page size
            if (pageSize > 1000) pageSize = 1000;
            if (pageSize < 1) pageSize = 50;

            using var connection = CreateConnection();
            connection.Open();

            // Build base query with cross-tenant access
            var whereConditions = new List<string> { "s.\"IsDeleted\" = false" };
            var parameters = new DynamicParameters();

            // Apply filters
            if (!string.IsNullOrEmpty(tenantId))
            {
                whereConditions.Add("s.\"TenantId\" = @TenantId");
                parameters.Add("TenantId", tenantId);
            }

            if (productId.HasValue)
            {
                whereConditions.Add("s.\"ProductId\" = @ProductId");
                parameters.Add("ProductId", productId.Value);
            }

            if (!string.IsNullOrEmpty(status))
            {
                whereConditions.Add("s.\"Status\" = @Status");
                parameters.Add("Status", status);
            }

            if (!string.IsNullOrEmpty(subscriptionType))
            {
                whereConditions.Add("s.\"SubscriptionType\" = @SubscriptionType");
                parameters.Add("SubscriptionType", subscriptionType);
            }

            if (isActive.HasValue)
            {
                whereConditions.Add("s.\"IsActive\" = @IsActive");
                parameters.Add("IsActive", isActive.Value);
            }

            if (!string.IsNullOrEmpty(pricingTier))
            {
                whereConditions.Add("s.\"PricingTier\" = @PricingTier");
                parameters.Add("PricingTier", pricingTier);
            }

            if (startDateFrom.HasValue)
            {
                whereConditions.Add("s.\"StartDate\" >= @StartDateFrom");
                parameters.Add("StartDateFrom", startDateFrom.Value);
            }

            if (startDateTo.HasValue)
            {
                whereConditions.Add("s.\"StartDate\" <= @StartDateTo");
                parameters.Add("StartDateTo", startDateTo.Value);
            }

            if (endDateFrom.HasValue)
            {
                whereConditions.Add("s.\"EndDate\" >= @EndDateFrom");
                parameters.Add("EndDateFrom", endDateFrom.Value);
            }

            if (endDateTo.HasValue)
            {
                whereConditions.Add("s.\"EndDate\" <= @EndDateTo");
                parameters.Add("EndDateTo", endDateTo.Value);
            }

            if (isExpired.HasValue)
            {
                var now = DateTime.UtcNow;
                if (isExpired.Value)
                {
                    whereConditions.Add("s.\"EndDate\" IS NOT NULL AND s.\"EndDate\" < @Now");
                }
                else
                {
                    whereConditions.Add("(s.\"EndDate\" IS NULL OR s.\"EndDate\" >= @Now)");
                }
                parameters.Add("Now", now);
            }

            if (expiringWithinDays.HasValue)
            {
                var futureDate = DateTime.UtcNow.AddDays(expiringWithinDays.Value);
                whereConditions.Add("s.\"EndDate\" IS NOT NULL AND s.\"EndDate\" <= @FutureDate AND s.\"EndDate\" >= @Now");
                parameters.Add("FutureDate", futureDate);
                parameters.Add("Now", DateTime.UtcNow);
            }

            if (!string.IsNullOrEmpty(searchTerm))
            {
                whereConditions.Add(@"(
                    LOWER(s.""SubscriptionType"") LIKE @SearchTerm OR 
                    LOWER(p.""Name"") LIKE @SearchTerm OR 
                    LOWER(s.""TenantId"") LIKE @SearchTerm OR 
                    LOWER(s.""PricingTier"") LIKE @SearchTerm
                )");
                parameters.Add("SearchTerm", $"%{searchTerm.ToLower()}%");
            }

            var whereClause = whereConditions.Any() ? $"WHERE {string.Join(" AND ", whereConditions)}" : "";

            // Build order by clause
            var orderByClause = BuildOrderByClause(orderBy, orderDirection);

            // Get total count
            var countSql = $@"
                SELECT COUNT(*)
                FROM ""Genp"".""Subscriptions"" s
                LEFT JOIN ""Genp"".""Products"" p ON s.""ProductId"" = p.""Id""
                {whereClause}";

            var totalCount = await connection.QuerySingleAsync<int>(countSql, parameters);

            // Get paginated data
            var dataSql = $@"
                SELECT
                    s.""Id"",
                    s.""TenantId"",
                    s.""TenantId"" as ""TenantName"",
                    s.""ProductId"",
                    p.""Name"" as ""ProductName"",
                    s.""SubscriptionType"",
                    s.""Status"",
                    s.""StartDate"",
                    s.""EndDate"",
                    s.""AutoRenew"",
                    s.""PricingTier"",
                    s.""Version"",
                    s.""TemplateJson"",
                    s.""TemplateDetails""::jsonb,
                    s.""IsActive"",
                    COALESCE(sm_count.""MetadataCount"", 0) as ""MetadataCount"",
                    s.""CreatedAt"",
                    s.""CreatedBy"",
                    s.""ModifiedAt"",
                    s.""ModifiedBy""
                FROM ""Genp"".""Subscriptions"" s
                LEFT JOIN ""Genp"".""Products"" p ON s.""ProductId"" = p.""Id""
                LEFT JOIN (
                    SELECT ""SubscriptionId"", COUNT(*) as ""MetadataCount""
                    FROM ""Genp"".""SubscriptionMetadata""
                    WHERE ""IsDeleted"" = false
                    GROUP BY ""SubscriptionId""
                ) sm_count ON s.""Id"" = sm_count.""SubscriptionId""
                {whereClause}
                {orderByClause}
                LIMIT @PageSize OFFSET @Offset";

            parameters.Add("PageSize", pageSize);
            parameters.Add("Offset", (pageNumber - 1) * pageSize);

            var subscriptions = await connection.QueryAsync<ComprehensiveSubscriptionDto>(dataSql, parameters);

            // Create response
            var response = new ComprehensiveSubscriptionResponseDto
            {
                Subscriptions = subscriptions.ToList(),
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            // Add summary if requested
            if (includeSummary)
            {
                response.Summary = await GenerateSummaryAsync(connection, whereClause, parameters);
            }

            _logger.LogInformation("Successfully retrieved {Count} comprehensive subscriptions", subscriptions.Count());
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching comprehensive subscription data");
            throw;
        }
    }

    private static string BuildOrderByClause(string orderBy, string orderDirection)
    {
        var isDescending = orderDirection.ToLower() == "desc";
        var direction = isDescending ? "DESC" : "ASC";

        var column = orderBy.ToLower() switch
        {
            "startdate" => "s.\"StartDate\"",
            "enddate" => "s.\"EndDate\"",
            "subscriptiontype" => "s.\"SubscriptionType\"",
            "status" => "s.\"Status\"",
            "tenantid" => "s.\"TenantId\"",
            "productname" => "p.\"Name\"",
            _ => "s.\"CreatedAt\""
        };

        return $"ORDER BY {column} {direction}";
    }

    private async Task<ComprehensiveSubscriptionSummaryDto> GenerateSummaryAsync(
        IDbConnection connection, 
        string whereClause, 
        DynamicParameters parameters)
    {
        var now = DateTime.UtcNow;
        var futureDate = now.AddDays(30);

        var summarySql = $@"
            SELECT
                COUNT(CASE WHEN s.""IsActive"" = true THEN 1 END) as ""ActiveSubscriptions"",
                COUNT(CASE WHEN s.""EndDate"" IS NOT NULL AND s.""EndDate"" < @SummaryNow THEN 1 END) as ""ExpiredSubscriptions"",
                COUNT(CASE WHEN s.""EndDate"" IS NOT NULL AND s.""EndDate"" <= @SummaryFutureDate AND s.""EndDate"" >= @SummaryNow THEN 1 END) as ""ExpiringIn30Days"",
                COUNT(DISTINCT s.""TenantId"") as ""UniqueTenants"",
                COUNT(DISTINCT s.""ProductId"") as ""UniqueProducts""
            FROM ""Genp"".""Subscriptions"" s
            LEFT JOIN ""Genp"".""Products"" p ON s.""ProductId"" = p.""Id""
            {whereClause}";

        parameters.Add("SummaryNow", now);
        parameters.Add("SummaryFutureDate", futureDate);

        var summaryData = await connection.QuerySingleAsync(summarySql, parameters);

        // Get status breakdown
        var statusSql = $@"
            SELECT s.""Status"", COUNT(*) as ""Count""
            FROM ""Genp"".""Subscriptions"" s
            LEFT JOIN ""Genp"".""Products"" p ON s.""ProductId"" = p.""Id""
            {whereClause}
            GROUP BY s.""Status""";

        var statusBreakdown = await connection.QueryAsync(statusSql, parameters);

        // Get type breakdown
        var typeSql = $@"
            SELECT s.""SubscriptionType"", COUNT(*) as ""Count""
            FROM ""Genp"".""Subscriptions"" s
            LEFT JOIN ""Genp"".""Products"" p ON s.""ProductId"" = p.""Id""
            {whereClause}
            GROUP BY s.""SubscriptionType""";

        var typeBreakdown = await connection.QueryAsync(typeSql, parameters);

        return new ComprehensiveSubscriptionSummaryDto
        {
            ActiveSubscriptions = (int)summaryData.ActiveSubscriptions,
            ExpiredSubscriptions = (int)summaryData.ExpiredSubscriptions,
            ExpiringIn30Days = (int)summaryData.ExpiringIn30Days,
            UniqueTenants = (int)summaryData.UniqueTenants,
            UniqueProducts = (int)summaryData.UniqueProducts,
            SubscriptionsByStatus = statusBreakdown.ToDictionary(x => (string)x.Status, x => (int)x.Count),
            SubscriptionsByType = typeBreakdown.ToDictionary(x => (string)x.SubscriptionType, x => (int)x.Count)
        };
    }
}
