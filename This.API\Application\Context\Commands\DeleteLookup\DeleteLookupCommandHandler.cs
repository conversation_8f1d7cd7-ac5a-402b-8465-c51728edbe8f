using Domain.Entities;
using Infrastructure.Database.Repositories.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Context.Commands.DeleteLookup;

/// <summary>
/// Handler for DeleteLookupCommand
/// </summary>
public class DeleteLookupCommandHandler : IRequestHandler<DeleteLookupCommand, Result<bool>>
{
    private readonly IRepository<Lookup> _lookupRepository;
    private readonly ILogger<DeleteLookupCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteLookupCommandHandler(
        IRepository<Lookup> lookupRepository,
        ILogger<DeleteLookupCommandHandler> logger)
    {
        _lookupRepository = lookupRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<bool>> Handle(DeleteLookupCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Get existing lookup
            var lookup = await _lookupRepository.GetByIdAsync(request.Id, cancellationToken);
            if (lookup == null)
            {
                return Result<bool>.Failure($"Lookup with ID '{request.Id}' not found.");
            }

            // Soft delete the lookup
            await _lookupRepository.DeleteAsync(lookup, cancellationToken);
            
            _logger.LogInformation("Deleted lookup with ID: {LookupId}", request.Id);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting lookup with ID: {LookupId}", request.Id);
            return Result<bool>.Failure("An error occurred while deleting the lookup.");
        }
    }
}
