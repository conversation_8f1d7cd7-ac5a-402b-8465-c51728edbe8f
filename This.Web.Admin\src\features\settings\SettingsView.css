/* Settings View Styles */

/* Settings container */
.settings-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* Theme transition animations */
:root {
  --theme-transition-duration: 300ms;
  --theme-transition-easing: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global theme transition */
.theme-transitioning,
.theme-transitioning *,
.theme-transitioning *::before,
.theme-transitioning *::after {
  transition: 
    background-color var(--theme-transition-duration) var(--theme-transition-easing),
    border-color var(--theme-transition-duration) var(--theme-transition-easing),
    color var(--theme-transition-duration) var(--theme-transition-easing),
    fill var(--theme-transition-duration) var(--theme-transition-easing),
    stroke var(--theme-transition-duration) var(--theme-transition-easing),
    box-shadow var(--theme-transition-duration) var(--theme-transition-easing) !important;
}

/* Color preview dots with CSS custom properties */
.theme-color-preview {
  --color: #000000;
  background-color: var(--color);
  transition: transform 200ms ease, box-shadow 200ms ease;
}

.theme-color-preview:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Theme option cards */
.theme-option-card {
  position: relative;
  overflow: hidden;
}

.theme-option-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s ease;
}

.theme-option-card:hover::before {
  left: 100%;
}

/* Enhanced animations */
@keyframes theme-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes theme-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes theme-fade-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Grid responsive adjustments */
@media (max-width: 768px) {
  .settings-container {
    padding: 0 1rem;
  }
  
  /* Stack theme cards on mobile */
  .theme-grid {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  /* Two columns on tablet */
  .theme-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1025px) {
  /* Three columns on desktop */
  .theme-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Accessibility improvements */
.settings-container:focus-within {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: 8px;
}

/* Category filter buttons */
.category-filter {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.category-filter button {
  transition: all 200ms ease;
}

.category-filter button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Theme status card */
.theme-status-card {
  background: linear-gradient(135deg, var(--color-muted) 0%, var(--color-muted-foreground) 100%);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  padding: 1rem;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .theme-transitioning,
  .theme-transitioning *,
  .theme-transitioning *::before,
  .theme-transitioning *::after {
    transition: none !important;
  }
  
  .theme-color-preview {
    transition: none;
  }
  
  .theme-option-card::before {
    display: none;
  }
  
  .theme-option-card:hover {
    transform: none;
  }
  
  .category-filter button:hover {
    transform: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .theme-color-preview {
    border-width: 2px;
    border-style: solid;
    border-color: currentColor;
  }
  
  .theme-option-card {
    border-width: 2px;
    border-style: solid;
  }
  
  .theme-option-card:hover {
    border-color: var(--color-primary);
  }
}

/* Dark theme specific adjustments */
[data-theme="dark"] .theme-color-preview:hover {
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.15);
}

[data-theme="dark"] .theme-option-card:hover {
  box-shadow: 0 4px 16px rgba(255, 255, 255, 0.1);
}

/* Loading states */
.theme-loading {
  opacity: 0.6;
  pointer-events: none;
}

.theme-loading * {
  cursor: wait !important;
}

/* Focus styles for better accessibility */
.theme-option-card:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Smooth hover transitions for cards */
.theme-option-card {
  transition: 
    transform 200ms ease,
    box-shadow 200ms ease,
    border-color 200ms ease;
}

.theme-option-card:hover {
  transform: translateY(-2px);
}

/* Information section styling */
.theme-info-section {
  background: var(--color-muted);
  border-radius: 0.5rem;
  padding: 1.5rem;
}

.theme-info-section h4 {
  color: var(--color-foreground);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.theme-info-section ul {
  list-style: none;
  padding: 0;
}

.theme-info-section li {
  color: var(--color-muted-foreground);
  margin-bottom: 0.25rem;
}

/* Responsive typography */
@media (max-width: 640px) {
  .settings-container h1 {
    font-size: 1.5rem;
  }
  
  .theme-option-card {
    padding: 1rem;
  }
}

/* Print styles */
@media print {
  .theme-option-card::before {
    display: none;
  }
  
  .theme-color-preview {
    border: 1px solid #000;
  }
}
