using Application.Context.Specifications;
using Domain.Entities;
using Infrastructure.Database.Repositories.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Context.Commands.UpsertBulkLookups;

/// <summary>
/// Command to upsert multiple lookups for a context
/// </summary>
public class UpsertBulkLookupsCommand : IRequest<Result<UpsertBulkLookupsResponse>>
{
    /// <summary>
    /// Context ID
    /// </summary>
    public Guid ContextId { get; set; }

    /// <summary>
    /// List of lookups to upsert
    /// </summary>
    public List<LookupRequest> Lookups { get; set; } = new();
}

/// <summary>
/// Individual lookup request
/// </summary>
public class LookupRequest
{
    /// <summary>
    /// Lookup ID (optional for insert)
    /// </summary>
    public Guid? Id { get; set; }

    /// <summary>
    /// Lookup value
    /// </summary>
    public string Value { get; set; } = string.Empty;

    /// <summary>
    /// Whether this is the default value
    /// </summary>
    public bool IsDefault { get; set; }

    /// <summary>
    /// Additional value 1
    /// </summary>
    public string? Value1 { get; set; }

    /// <summary>
    /// Additional value 2
    /// </summary>
    public string? Value2 { get; set; }

    /// <summary>
    /// Display sequence
    /// </summary>
    public int ShowSequence { get; set; } = 1;

    /// <summary>
    /// Whether the lookup is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Response for bulk lookup upsert
/// </summary>
public class UpsertBulkLookupsResponse
{
    /// <summary>
    /// Number of lookups created
    /// </summary>
    public int CreatedCount { get; set; }

    /// <summary>
    /// Number of lookups updated
    /// </summary>
    public int UpdatedCount { get; set; }

    /// <summary>
    /// List of created/updated lookup IDs
    /// </summary>
    public List<Guid> LookupIds { get; set; } = new();

    /// <summary>
    /// Any validation errors
    /// </summary>
    public List<string> Errors { get; set; } = new();
}
