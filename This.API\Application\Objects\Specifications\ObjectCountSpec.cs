using Ardalis.Specification;
using ObjectEntity = Domain.Entities.Object;

namespace Application.Objects.Specifications;

/// <summary>
/// Specification to count Objects with filters
/// </summary>
public class ObjectCountSpec : Specification<ObjectEntity>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectCountSpec(
        string? searchTerm = null,
        Guid? featureId = null, // Keep parameter for compatibility but ignore
        bool? isActive = null)
    {
        Query.Where(o => !o.IsDeleted);

        // Apply filters
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            Query.Where(o => o.Name.Contains(searchTerm) ||
                           (o.Description != null && o.Description.Contains(searchTerm)));
        }

        // Feature references removed - featureId parameter ignored

        if (isActive.HasValue)
        {
            Query.Where(o => o.IsActive == isActive.Value);
        }
    }
}
