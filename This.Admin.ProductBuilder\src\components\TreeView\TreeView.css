/* Main Tree View Container */
.tree-view {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background-color: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* Header Styles */
.tree-header {
  background: linear-gradient(to right, #f8f9fa, #f1f3f5);
  font-weight: 600;
  border-bottom: 1px solid #e9ecef;
  padding: 0.75rem 1.25rem !important;
  color: #212529;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Content Area */
.tree-content {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem 0.25rem;
  background-color: #fff;
}

.tree-node {
  margin: 2px 0;
}

/* Node Content */
.node-content {
  padding: 0.4rem 0.75rem 0.4rem 0.5rem;
  margin: 0.15rem 0.25rem;
  border-radius: 4px;
  cursor: pointer;
  will-change: transform, background-color;
  transition: transform 0.15s ease-out, background-color 0.15s ease-out;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  border-left: 3px solid transparent;
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-font-smoothing: subpixel-antialiased;
}

.node-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background-color: currentColor;
  opacity: 0.1;
  z-index: 0;
  transform: translateZ(0);
}

.node-content > * {
  position: relative;
  z-index: 1;
  transform: translateZ(0);
}

.node-content:hover {
  background-color: rgba(0, 0, 0, 0.03);
  transform: translateX(2px) translateZ(0);
}

.node-content.selected {
  background-color: rgba(0, 0, 0, 0.05);
  font-weight: 500;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);
  transform: translateZ(0);
}

.node-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
}

/* Node Children */
.node-children {
  margin-left: 1.5rem;
  border-left: 1px dashed #e9ecef;
  padding-left: 0.75rem;
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
  will-change: transform, opacity;
  animation: fadeIn 0.15s ease-out forwards;
  opacity: 0;
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-font-smoothing: subpixel-antialiased;
}

/* Add Folder Form */
.add-folder-form {
  margin: 0.5rem 0.5rem 0.5rem 2rem;
  padding: 0.75rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 6px;
  border: 1px dashed #e9ecef;
}

.add-folder-form form {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.add-folder-form .form-row {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.add-folder-form input,
.add-folder-form select {
  flex: 1;
  min-width: 0;
  padding: 0.35rem 0.5rem;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 0.9rem;
}

.add-folder-form .button-group {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 0.25rem;
}

.add-folder-form button {
  padding: 0.3rem 0.75rem;
  font-size: 0.85rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.add-folder-form button[type="submit"] {
  background-color: #0d6efd;
  color: white;
  border: 1px solid #0d6efd;
}

.add-folder-form button[type="button"] {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #495057;
}

.add-folder-form button:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}

/* Footer & Legend */
.tree-footer {
  padding: 0.75rem 1.25rem;
  border-top: 1px solid #e9ecef;
  font-size: 0.8rem;
  color: #6c757d;
  background-color: #f8f9fa;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.legend-item {
  display: inline-flex;
  align-items: center;
  margin-right: 1rem;
  margin-bottom: 0.25rem;
  padding: 0.25rem 0.6rem;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  border: 1px solid #e9ecef;
  font-size: 0.8rem;
  line-height: 1.2;
}

.legend-color {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 0.5rem;
  flex-shrink: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Badge styles for node types */
.node-name .badge {
  font-weight: 500;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  font-size: 0.6em !important;
  padding: 0.2em 0.5em !important;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  margin-left: 0.5rem;
  white-space: nowrap;
}

/* Scrollbar styles */
.tree-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.tree-content::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 4px;
}

.tree-content::-webkit-scrollbar-thumb {
  background: #ced4da;
  border-radius: 4px;
  border: 2px solid #f8f9fa;
}

.tree-content::-webkit-scrollbar-thumb:hover {
  background: #adb5bd;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-3px) translateZ(0); }
  to { opacity: 1; transform: translateY(0) translateZ(0); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .tree-header {
    padding: 0.6rem 1rem !important;
    font-size: 0.9rem;
  }
  
  .tree-footer {
    padding: 0.6rem 1rem;
    font-size: 0.75rem;
  }
  
  .legend-item {
    margin-right: 0.75rem;
    padding: 0.2rem 0.5rem;
  }
  
  .add-folder-form {
    margin-left: 1rem;
    padding: 0.5rem;
  }
  
  .add-folder-form .form-row {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .add-folder-form input,
  .add-folder-form select {
    width: 100%;
  }
}
