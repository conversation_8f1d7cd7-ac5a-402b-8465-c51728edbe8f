using Application.Subscriptions.Commands;
using Application.Subscriptions.DTOs;
using Application.Subscriptions.Queries;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// Subscriptions controller
/// </summary>
[Route("api/[controller]")]
public class SubscriptionsController : BaseApiController
{
    /// <summary>
    /// Get all subscriptions with pagination and filtering
    /// </summary>
    [HttpGet]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<SubscriptionDto>>> GetSubscriptions([FromQuery] GetSubscriptionsQuery query)
    {
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get subscription by ID
    /// </summary>
    [HttpGet("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<SubscriptionDto>>> GetSubscriptionById(Guid id)
    {
        return Ok(await Mediator.Send(new GetSubscriptionByIdQuery(id)));
    }

    /// <summary>
    /// Create a new subscription
    /// </summary>
    [HttpPost]
    [TenantIdHeader]
    public async Task<ActionResult<Result<SubscriptionDto>>> CreateSubscription([FromBody] CreateSubscriptionDto dto)
    {
        var command = new CreateSubscriptionCommand
        {
            ProductId = dto.ProductId,
            SubscriptionType = dto.SubscriptionType,
            Status = dto.Status,
            StartDate = dto.StartDate,
            EndDate = dto.EndDate,
            AutoRenew = dto.AutoRenew,
            PricingTier = dto.PricingTier,
            Version = dto.Version,
            TemplateJson = dto.TemplateJson,
            IsActive = dto.IsActive,
            TemplateDetails = dto.TemplateDetails,
        };

        var result = await Mediator.Send(command);

        if (result.Succeeded)
        {
            return CreatedAtAction(nameof(GetSubscriptionById), new { id = result.Data!.Id }, result);
        }

        return BadRequest(result);
    }

    /// <summary>
    /// Update an existing subscription
    /// </summary>
    [HttpPut("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<SubscriptionDto>>> UpdateSubscription(Guid id, [FromBody] UpdateSubscriptionDto dto)
    {
        var command = new UpdateSubscriptionCommand
        {
            Id = id,
            ProductId = dto.ProductId,
            SubscriptionType = dto.SubscriptionType,
            Status = dto.Status,
            StartDate = dto.StartDate,
            EndDate = dto.EndDate,
            AutoRenew = dto.AutoRenew,
            PricingTier = dto.PricingTier,
            Version = dto.Version,
            TemplateJson = dto.TemplateJson,
            IsActive = dto.IsActive,
            TemplateDetails = dto.TemplateDetails,
        };

        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Delete a subscription (soft delete)
    /// </summary>
    [HttpDelete("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<bool>>> DeleteSubscription(Guid id)
    {
        return Ok(await Mediator.Send(new DeleteSubscriptionCommand(id)));
    }

    /// <summary>
    /// Get subscriptions by product ID
    /// </summary>
    [HttpGet("by-product/{productId}")]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<SubscriptionDto>>> GetSubscriptionsByProduct(
        Guid productId,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? status = null,
        [FromQuery] bool? isActive = null)
    {
        var query = new GetSubscriptionsQuery
        {
            ProductId = productId,
            PageNumber = pageNumber,
            PageSize = pageSize,
            Status = status,
            IsActive = isActive
        };

        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get subscriptions by status
    /// </summary>
    [HttpGet("by-status/{status}")]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<SubscriptionDto>>> GetSubscriptionsByStatus(
        string status,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] Guid? productId = null,
        [FromQuery] bool? isActive = null)
    {
        var query = new GetSubscriptionsQuery
        {
            Status = status,
            PageNumber = pageNumber,
            PageSize = pageSize,
            ProductId = productId,
            IsActive = isActive
        };

        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get active subscriptions
    /// </summary>
    [HttpGet("active")]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<SubscriptionDto>>> GetActiveSubscriptions(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] Guid? productId = null,
        [FromQuery] string? searchTerm = null)
    {
        var query = new GetSubscriptionsQuery
        {
            IsActive = true,
            PageNumber = pageNumber,
            PageSize = pageSize,
            ProductId = productId,
            SearchTerm = searchTerm
        };

        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get expired subscriptions
    /// </summary>
    [HttpGet("expired")]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<SubscriptionDto>>> GetExpiredSubscriptions(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] Guid? productId = null)
    {
        var query = new GetSubscriptionsQuery
        {
            PageNumber = pageNumber,
            PageSize = pageSize,
            ProductId = productId,
            // Note: You might want to add an EndDate filter in the query handler for this
        };

        return Ok(await Mediator.Send(query));
    }
}
