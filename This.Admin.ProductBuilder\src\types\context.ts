// Context Management Types

export interface Context {
  id: string;
  name: string;
  description?: string;
  category?: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  createdBy?: string;
  modifiedAt?: string;
  modifiedBy?: string;
}

export interface Lookup {
  id: string;
  contextId: string;
  value: string;
  isDefault: boolean;
  value1?: string;
  value2?: string;
  showSequence: number;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  createdBy?: string;
  modifiedAt?: string;
  modifiedBy?: string;
}

export interface ContextWithLookups {
  context: Context;
  lookups: Lookup[];
  lookupsCount: number;
}

export interface CreateContextRequest {
  name: string;
  description?: string;
  category?: string;
  isActive?: boolean;
}

export interface UpdateContextRequest {
  id: string;
  name: string;
  description?: string;
  category?: string;
  isActive?: boolean;
}

export interface CreateLookupRequest {
  contextId: string;
  value: string;
  isDefault?: boolean;
  value1?: string;
  value2?: string;
  showSequence?: number;
  isActive?: boolean;
}

export interface UpdateLookupRequest {
  id: string;
  contextId: string;
  value: string;
  isDefault?: boolean;
  value1?: string;
  value2?: string;
  showSequence?: number;
  isActive?: boolean;
}

export interface BulkLookupRequest {
  contextId: string;
  lookups: Array<{
    id?: string;
    value: string;
    isDefault?: boolean;
    value1?: string;
    value2?: string;
    showSequence?: number;
    isActive?: boolean;
  }>;
}

export interface BulkLookupResponse {
  createdCount: number;
  updatedCount: number;
  lookupIds: string[];
  errors: string[];
}

// TenantContext Types
export interface TenantContext {
  id: string;
  name: string;
  description?: string;
  category?: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  createdBy?: string;
  modifiedAt?: string;
  modifiedBy?: string;
}

export interface TenantLookup {
  id: string;
  tenantContextId: string;
  value: string;
  isDefault: boolean;
  value1?: string;
  value2?: string;
  showSequence: number;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  createdBy?: string;
  modifiedAt?: string;
  modifiedBy?: string;
}

export interface TenantContextWithLookups {
  tenantContext: TenantContext;
  tenantLookups: TenantLookup[];
  tenantLookupsCount: number;
}

export interface CreateTenantContextRequest {
  name: string;
  description?: string;
  category?: string;
  isActive?: boolean;
}

export interface UpdateTenantContextRequest {
  id: string;
  name: string;
  description?: string;
  category?: string;
  isActive?: boolean;
}

export interface CreateTenantLookupRequest {
  tenantContextId: string;
  value: string;
  isDefault?: boolean;
  value1?: string;
  value2?: string;
  showSequence?: number;
  isActive?: boolean;
}

export interface UpdateTenantLookupRequest {
  id: string;
  tenantContextId: string;
  value: string;
  isDefault?: boolean;
  value1?: string;
  value2?: string;
  showSequence?: number;
  isActive?: boolean;
}

// Object Types
export interface ObjectDto {
  id: string;
  featureId?: string;
  featureName?: string;
  parentObjectId?: string;
  parentObjectName?: string;
  name: string;
  description?: string;
  isActive: boolean;
  childObjectsCount?: number;
  metadataCount?: number;
}

// ObjectLookup Types
export interface ObjectLookup {
  id: string;
  name: string;
  sourceType: string;
  objectId?: string;
  displayField: string;
  valueField: string;
  metadataFieldForDisplay?: string;
  metadataFieldForValue?: string;
  supportsTenantFiltering: boolean;
  sortBy: string;
  sortOrder: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  createdBy?: string;
  modifiedAt?: string;
  modifiedBy?: string;
}

export interface CreateObjectLookupRequest {
  name: string;
  sourceType: string;
  objectId?: string;
  displayField?: string;
  valueField?: string;
  metadataFieldForDisplay?: string;
  metadataFieldForValue?: string;
  supportsTenantFiltering?: boolean;
  sortBy?: string;
  sortOrder?: string;
  isActive?: boolean;
}

export interface UpdateObjectLookupRequest {
  id: string;
  name: string;
  sourceType: string;
  objectId?: string;
  displayField?: string;
  valueField?: string;
  metadataFieldForDisplay?: string;
  metadataFieldForValue?: string;
  supportsTenantFiltering?: boolean;
  sortBy?: string;
  sortOrder?: string;
  isActive?: boolean;
}
