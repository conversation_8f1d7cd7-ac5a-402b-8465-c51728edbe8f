using MediatR;
using Shared.Common.Response;

namespace Application.Context.Commands.UpsertTenantLookup;

/// <summary>
/// Command to upsert a single tenant lookup
/// </summary>
public class UpsertTenantLookupCommand : IRequest<Result<Guid>>
{
    /// <summary>
    /// TenantLookup ID (optional for insert)
    /// </summary>
    public Guid? Id { get; set; }

    /// <summary>
    /// TenantContext ID
    /// </summary>
    public Guid TenantContextId { get; set; }

    /// <summary>
    /// Lookup value
    /// </summary>
    public string Value { get; set; } = string.Empty;

    /// <summary>
    /// Whether this is the default value
    /// </summary>
    public bool IsDefault { get; set; }

    /// <summary>
    /// Additional value 1
    /// </summary>
    public string? Value1 { get; set; }

    /// <summary>
    /// Additional value 2
    /// </summary>
    public string? Value2 { get; set; }

    /// <summary>
    /// Display sequence order
    /// </summary>
    public int ShowSequence { get; set; } = 0;

    /// <summary>
    /// Whether the tenant lookup is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
