import React, { useCallback, useState, useEffect } from 'react';
import { <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap';
import { DataGrid, type Column } from 'react-data-grid';
import 'react-data-grid/lib/styles.css';

import { type TreeNode } from '../TreeView/TreeView';
import { type MetadataItem, generateGuid } from '../../utils/index';
import { CustomTextEditor } from './CustomTextEditor';
import { CustomTypeEditor } from './CustomTypeEditor';
import { CustomContextEditor } from './CustomContextEditor';
import { DisplayActionModal } from './DisplayActionModal';
import { DisplayActionSummary } from './DisplayActionSummary';

interface MetadataEditorProps {
  selectedNode: TreeNode | null;
  tempMetadata: MetadataItem[];
  setTempMetadata: React.Dispatch<React.SetStateAction<MetadataItem[]>>;
  setHasUnsavedChanges: React.Dispatch<React.SetStateAction<boolean>>;
  setTreeData: React.Dispatch<React.SetStateAction<TreeNode[]>>;
  setSelectedNode: React.Dispatch<React.SetStateAction<TreeNode | null>>;
}

interface RenderCellProps {
  row: any;
  onRowChange?: (row: any) => void;
}

export const MetadataEditor: React.FC<MetadataEditorProps> = ({
  selectedNode,
  tempMetadata,
  setTempMetadata,
  setHasUnsavedChanges,
  setTreeData,
  setSelectedNode
}) => {
  // Helper function to update a specific row in tempMetadata
  const updateTempMetadataRow = useCallback((rowId: string, updates: Partial<MetadataItem>) => {
    setTempMetadata(prev => {
      const updatedRows = prev.map(row =>
        row._internalId === rowId ? { ...row, ...updates } : row
      );
      return updatedRows;
    });
    setHasUnsavedChanges(true);
  }, [setTempMetadata, setHasUnsavedChanges]);

  // Auto-save metadata changes after a delay
  useEffect(() => {
    if (!selectedNode?.type || tempMetadata.length === 0) return;

    const timeoutId = setTimeout(() => {
      handleSaveMetadata();
    }, 2000); // Auto-save after 2 seconds of inactivity

    return () => clearTimeout(timeoutId);
  }, [tempMetadata]); // Only depend on tempMetadata changes

  const handleAddMetadata = useCallback(() => {
    if (!selectedNode?.type) return;

    const defaultName = `new_${selectedNode.type}_${Date.now()}`;

    const newItem: MetadataItem = {
      _internalId: generateGuid(),
      name: defaultName as any,
      type: 'Text',
      description: 'Custom field',
      required: false,
      isActive: true,
      contextId: undefined,
      contextName: undefined
    };

    setTempMetadata(prev => [...prev, newItem]);
    setHasUnsavedChanges(true);
  }, [selectedNode, setTempMetadata, setHasUnsavedChanges]);

  const [showDisplayActionModal, setShowDisplayActionModal] = useState(false);

  const handleConfigureDisplayActions = () => {
    setShowDisplayActionModal(true);
  };

  const handleSaveDisplayActions = (selectedDisplays: any[]) => {
    if (!selectedNode?.type) return;

    console.log('🔍 Saving display actions:', selectedDisplays);

    // Transform the displays to include proper UI structure with actions
    const transformedDisplays = selectedDisplays.map(display => ({
      id: display.id || generateGuid(),
      name: display.name,
      displayName: display.displayName,
      isDefault: display.isDefault,
      isSelected: true, // Mark as selected since these are the saved ones
      sortOrder: display.sortOrder || 1,
      routeTemplate: display.routeTemplate,
      icon: display.icon,
      actions: display.actions ? display.actions.map((action: any) => ({
        id: action.id || generateGuid(),
        name: action.name,
        description: action.description || `${action.name} action`,
        type: action.endpointTemplate ? 'API' : 'Navigation',
        endpoint: action.endpointTemplate,
        navigationTarget: action.navigationTarget,
        buttonStyle: action.buttonStyle,
        icon: action.icon,
        isSelected: true, // Mark as selected since these are the saved ones
        confirmationMessage: action.confirmationMessage,
        successMessage: action.successMessage,
        errorMessage: action.errorMessage
      })) : []
    }));

    const updatedNode = {
      ...selectedNode,
      displays: transformedDisplays
    };

    console.log('🔍 Updated node with displays:', updatedNode);

    const updateNodeInTree = (nodes: TreeNode[]): boolean => {
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].id === selectedNode.id) {
          nodes[i] = updatedNode;
          console.log('🔍 Node updated in tree:', nodes[i]);
          return true;
        }

        if (nodes[i].children && nodes[i].children!.length > 0) {
          if (updateNodeInTree(nodes[i].children!)) {
            return true;
          }
        }
      }
      return false;
    };

    setTreeData(prevData => {
      const newData = [...prevData];
      const updated = updateNodeInTree(newData);
      console.log('🔍 Tree data updated:', updated, newData);
      return newData;
    });

    setSelectedNode(updatedNode);
    setHasUnsavedChanges(true);
  };

  const handleSaveMetadata = useCallback(() => {
    if (!selectedNode?.type) return;

    const updatedNode = {
      ...selectedNode,
      metadata: [...tempMetadata]
    };

    const updateNodeInTree = (nodes: TreeNode[]): boolean => {
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].id === selectedNode.id) {
          nodes[i] = updatedNode;
          return true;
        }

        if (nodes[i].children && nodes[i].children!.length > 0) {
          if (updateNodeInTree(nodes[i].children!)) {
            return true;
          }
        }
      }
      return false;
    };

    setTreeData(prevData => {
      const newData = [...prevData];
      updateNodeInTree(newData);
      return newData;
    });

    setSelectedNode(updatedNode);
    setHasUnsavedChanges(false);
  }, [selectedNode, tempMetadata, setTreeData, setSelectedNode, setHasUnsavedChanges]);

  const metadataColumns: Column<MetadataItem>[] = [
    {
      key: 'name',
      name: 'Name',
      renderEditCell: (props) => <CustomTextEditor {...props} updateTempMetadataRow={updateTempMetadataRow} />,
    },
    {
      key: 'type',
      name: 'Type',
      renderEditCell: (props) => <CustomTypeEditor {...props} updateTempMetadataRow={updateTempMetadataRow} />,
    },
    {
      key: 'description',
      name: 'Description',
      renderEditCell: (props) => <CustomTextEditor {...props} updateTempMetadataRow={updateTempMetadataRow} />,
    },
    {
      key: 'required',
      name: 'Required',
      renderCell: (props: any) => (
        <input
          type="checkbox"
          checked={props.row.required}
          onChange={(e) => {
            updateTempMetadataRow(props.row._internalId, { required: e.target.checked });
          }}
          style={{ margin: '8px' }}
        />
      ),
    },
    {
      key: 'defaultValue',
      name: 'Default Value',
      renderEditCell: (props) => <CustomTextEditor {...props} updateTempMetadataRow={updateTempMetadataRow} />,
    },
    {
      key: 'isActive',
      name: 'Active',
      renderCell: (props: any) => (
        <input
          type="checkbox"
          checked={props.row.isActive}
          onChange={(e) => {
            updateTempMetadataRow(props.row._internalId, { isActive: e.target.checked });
          }}
          style={{ margin: '8px' }}
        />
      ),
    },
    {
      key: 'contextName',
      name: 'Context',
      renderCell: (props: any) => (
        <span style={{ fontSize: '0.875rem' }}>
          {props.row.contextName || '-'}
        </span>
      ),
      renderEditCell: (props) => <CustomContextEditor {...props} updateTempMetadataRow={updateTempMetadataRow} />,
    },
    {
      key: 'actions',
      name: 'Actions',
      renderCell: (props: RenderCellProps) => (
        <div className="d-flex gap-1">
          <Button
            variant="link"
            size="sm"
            className="text-danger p-0"
            onClick={() => {
              if (!selectedNode?.type) return;
              const updatedTempRows = tempMetadata.filter((item: any) => item._internalId !== props.row._internalId);
              setTempMetadata(updatedTempRows);
              setHasUnsavedChanges(true);
            }}
          >
            Remove
          </Button>
        </div>
      ),
    },
  ];

  if (!selectedNode) {
    return (
      <div className="flex-grow-1 d-flex align-items-center justify-content-center">
        <div className="text-center text-muted">
          <h5>No Node Selected</h5>
          <p>Select a node from the tree to view and edit its metadata</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="tree-header d-flex justify-content-between align-items-center p-2 border-bottom">
        <span>Metadata Mapper</span>
      </div>
      <div className="flex-grow-1 p-4 d-flex flex-column">
        <div className="mb-4">
          <h4 className="mb-3">{selectedNode.name}</h4>
          <div className="d-flex flex-wrap gap-2 mb-3">
            <Badge bg="secondary">Type: {selectedNode.type}</Badge>
            {selectedNode.children && (
              <Badge bg="light" text="dark">
                {selectedNode.children.length} {selectedNode.children.length === 1 ? 'child' : 'children'}
              </Badge>
            )}
          </div>
        </div>
        
        <div className="flex-grow-1 d-flex flex-column">
          {/* Display & Action Configuration for Object nodes */}
          {selectedNode.type === 'object' && (
            <>
              {/* Configuration Summary */}
              <DisplayActionSummary selectedNode={selectedNode} />

              {/* Configuration Button */}
              <div className="mb-4">
                <div className="d-flex justify-content-between align-items-center p-3 bg-light rounded border">
                  <div>
                    <h6 className="mb-1">Manage Configuration</h6>
                    <small className="text-muted">Add, edit, or remove displays and actions</small>
                  </div>
                  <Button variant="primary" onClick={handleConfigureDisplayActions}>
                    Configure Display & Actions
                  </Button>
                </div>
              </div>
            </>
          )}

          <div className="d-flex justify-content-between align-items-center mb-3">
            <div className="d-flex align-items-center">
              <h5 className="mb-0 me-2">Metadata</h5>
              <Button
                variant="outline-primary"
                size="sm"
                onClick={handleAddMetadata}
                title="Add Metadata"
                className="p-0 d-flex align-items-center justify-content-center me-2"
                style={{ width: '24px', height: '24px' }}
              >
                +
              </Button>
            </div>
            <Button
              variant="primary"
              size="sm"
              onClick={handleSaveMetadata}
              className="d-flex align-items-center gap-1"
            >
              💾 Save Changes
            </Button>
          </div>

          <div className="flex-grow-1" style={{ minHeight: '300px' }}>
            <DataGrid
              columns={metadataColumns}
              rows={tempMetadata}
              rowKeyGetter={(row) => row._internalId || ''}
              className="rdg-light"
              style={{ height: '100%' }}
            />
          </div>
        </div>

        {/* Display Action Modal */}
        <DisplayActionModal
          show={showDisplayActionModal}
          onHide={() => setShowDisplayActionModal(false)}
          selectedNode={selectedNode}
          onSave={handleSaveDisplayActions}
        />
      </div>
    </>
  );
};
