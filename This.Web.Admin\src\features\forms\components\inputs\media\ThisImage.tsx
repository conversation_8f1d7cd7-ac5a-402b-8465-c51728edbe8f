// components/ThisImage.tsx
import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  Image as ImageIcon,
  X,
  Check,
  AlertCircle,
  Download,
  ZoomIn
} from 'lucide-react';

interface ThisImageProps {
  id: string;
  label: string;
  value: File[];
  onChange: (files: File[]) => void;
  onValidation?: (errors: string[]) => void;
  disabled?: boolean;
  readOnly?: boolean;
  helpText?: string;
  placeholder?: string;
  required?: boolean;
  multiple?: boolean;
  maxFiles?: number;
  minFiles?: number;
  maxFileSize?: number; // in bytes
  minFileSize?: number; // in bytes
  maxTotalSize?: number; // total size of all files in bytes
  // Image-specific validations
  maxWidth?: number; // in pixels
  minWidth?: number; // in pixels
  maxHeight?: number; // in pixels
  minHeight?: number; // in pixels
  aspectRatio?: number; // width/height ratio
  aspectRatioTolerance?: number; // tolerance for aspect ratio (default: 0.1)
  allowedFormats?: string[]; // ['jpeg', 'png', 'gif', 'webp', 'svg']
  blockedFormats?: string[]; // formats to block
  maxMegapixels?: number; // maximum resolution in megapixels
  minMegapixels?: number; // minimum resolution in megapixels
  // Display options
  showPreview?: boolean;
  previewSize?: 'small' | 'medium' | 'large';
  showImageInfo?: boolean;
  showDimensions?: boolean;
  allowDownload?: boolean;
  allowRemove?: boolean;
  allowZoom?: boolean;
  dragAndDrop?: boolean;
  customValidation?: (files: File[], imageData: ImageData[]) => string | null;
}

interface ImageData {
  file: File;
  width: number;
  height: number;
  megapixels: number;
  aspectRatio: number;
  url: string;
}

interface ValidationRule {
  test: (files: File[], imageData: ImageData[]) => boolean;
  message: string;
}

const ThisImage: React.FC<ThisImageProps> = ({
  id,
  label,
  value,
  onChange,
  onValidation,
  disabled = false,
  readOnly = false,
  helpText,
  placeholder = 'Choose images or drag and drop',
  required = false,
  multiple = false,
  maxFiles = 10,
  minFiles = 0,
  maxFileSize = 10 * 1024 * 1024, // 10MB
  minFileSize = 0,
  maxTotalSize = 100 * 1024 * 1024, // 100MB
  maxWidth,
  minWidth,
  maxHeight,
  minHeight,
  aspectRatio,
  aspectRatioTolerance = 0.1,
  allowedFormats = ['jpeg', 'jpg', 'png', 'gif', 'webp'],
  blockedFormats,
  maxMegapixels,
  minMegapixels,
  showPreview = true,
  previewSize = 'medium',
  showImageInfo = true,
  showDimensions = true,
  allowDownload = false,
  allowRemove = true,
  allowZoom = true,
  dragAndDrop = true,
  customValidation
}) => {
  const [errors, setErrors] = useState<string[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isValidated, setIsValidated] = useState(false);
  const [imageData, setImageData] = useState<ImageData[]>([]);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Helper functions
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileExtension = (filename: string): string => {
    return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2).toLowerCase();
  };

  const getTotalFileSize = (files: File[]): number => {
    // Safety check: ensure files is an array
    if (!Array.isArray(files)) {
      console.warn('getTotalFileSize: files is not an array:', files);
      return 0;
    }
    return files.reduce((total, file) => total + file.size, 0);
  };

  const loadImageData = useCallback(async (files: File[]): Promise<ImageData[]> => {
    const imageDataPromises = files.map((file) => {
      return new Promise<ImageData>((resolve, reject) => {
        const img = new Image();
        const url = URL.createObjectURL(file);

        img.onload = () => {
          const width = img.naturalWidth;
          const height = img.naturalHeight;
          const megapixels = (width * height) / 1000000;
          const aspectRatio = width / height;

          resolve({
            file,
            width,
            height,
            megapixels,
            aspectRatio,
            url
          });
        };

        img.onerror = () => {
          reject(new Error(`Failed to load image: ${file.name}`));
        };

        img.src = url;
      });
    });

    try {
      return await Promise.all(imageDataPromises);
    } catch (error) {
      console.error('Error loading image data:', error);
      return [];
    }
  }, []);

  // Update image data when files change
  useEffect(() => {
    // Safety check: ensure value is an array
    const safeValue = Array.isArray(value) ? value : [];
    if (safeValue.length > 0) {
      loadImageData(safeValue).then(setImageData);
    } else {
      setImageData([]);
    }
  }, [value, loadImageData]);

  // Validation rules in priority order
  const getValidationRules = (): ValidationRule[] => {
    const rules: ValidationRule[] = [];

    // 1. Required validation (highest priority)
    if (required) {
      rules.push({
        test: (files) => files.length > 0,
        message: `${label} is required`
      });
    }

    // 2. File count validation
    if (minFiles > 0) {
      rules.push({
        test: (files) => {
          if (files.length === 0) return !required;
          return files.length >= minFiles;
        },
        message: `${label} must have at least ${minFiles} image${minFiles > 1 ? 's' : ''}`
      });
    }

    if (maxFiles > 0) {
      rules.push({
        test: (files) => {
          if (files.length === 0) return !required;
          return files.length <= maxFiles;
        },
        message: `${label} can have at most ${maxFiles} image${maxFiles > 1 ? 's' : ''}`
      });
    }

    // 3. File size validation
    if (maxFileSize > 0) {
      rules.push({
        test: (files) => {
          if (files.length === 0) return !required;
          return files.every(file => file.size <= maxFileSize);
        },
        message: `Each image must be smaller than ${formatFileSize(maxFileSize)}`
      });
    }

    if (minFileSize > 0) {
      rules.push({
        test: (files) => {
          if (files.length === 0) return !required;
          return files.every(file => file.size >= minFileSize);
        },
        message: `Each image must be larger than ${formatFileSize(minFileSize)}`
      });
    }

    // 4. Total size validation
    if (maxTotalSize > 0) {
      rules.push({
        test: (files) => {
          if (files.length === 0) return !required;
          return getTotalFileSize(files) <= maxTotalSize;
        },
        message: `Total image size must be smaller than ${formatFileSize(maxTotalSize)}`
      });
    }

    // 5. Image format validation
    if (allowedFormats && allowedFormats.length > 0) {
      rules.push({
        test: (files) => {
          if (files.length === 0) return !required;
          return files.every(file => allowedFormats.includes(getFileExtension(file.name)));
        },
        message: `Only these image formats are allowed: ${allowedFormats.join(', ')}`
      });
    }

    if (blockedFormats && blockedFormats.length > 0) {
      rules.push({
        test: (files) => {
          if (files.length === 0) return !required;
          return !files.some(file => blockedFormats.includes(getFileExtension(file.name)));
        },
        message: `These image formats are not allowed: ${blockedFormats.join(', ')}`
      });
    }

    // 6. Image dimension validation
    if (maxWidth) {
      rules.push({
        test: (files, imgData) => {
          if (files.length === 0) return !required;
          return imgData.every(data => data.width <= maxWidth);
        },
        message: `Image width must be at most ${maxWidth} pixels`
      });
    }

    if (minWidth) {
      rules.push({
        test: (files, imgData) => {
          if (files.length === 0) return !required;
          return imgData.every(data => data.width >= minWidth);
        },
        message: `Image width must be at least ${minWidth} pixels`
      });
    }

    if (maxHeight) {
      rules.push({
        test: (files, imgData) => {
          if (files.length === 0) return !required;
          return imgData.every(data => data.height <= maxHeight);
        },
        message: `Image height must be at most ${maxHeight} pixels`
      });
    }

    if (minHeight) {
      rules.push({
        test: (files, imgData) => {
          if (files.length === 0) return !required;
          return imgData.every(data => data.height >= minHeight);
        },
        message: `Image height must be at least ${minHeight} pixels`
      });
    }

    // 7. Aspect ratio validation
    if (aspectRatio) {
      rules.push({
        test: (files, imgData) => {
          if (files.length === 0) return !required;
          return imgData.every(data =>
            Math.abs(data.aspectRatio - aspectRatio) <= aspectRatioTolerance
          );
        },
        message: `Image aspect ratio must be approximately ${aspectRatio.toFixed(2)} (${aspectRatio > 1 ? 'landscape' : aspectRatio < 1 ? 'portrait' : 'square'})`
      });
    }

    // 8. Megapixel validation
    if (maxMegapixels) {
      rules.push({
        test: (files, imgData) => {
          if (files.length === 0) return !required;
          return imgData.every(data => data.megapixels <= maxMegapixels);
        },
        message: `Image resolution must be at most ${maxMegapixels} megapixels`
      });
    }

    if (minMegapixels) {
      rules.push({
        test: (files, imgData) => {
          if (files.length === 0) return !required;
          return imgData.every(data => data.megapixels >= minMegapixels);
        },
        message: `Image resolution must be at least ${minMegapixels} megapixels`
      });
    }

    // 9. Custom validation
    if (customValidation) {
      rules.push({
        test: (files, imgData) => {
          const customError = customValidation(files, imgData);
          return customError === null;
        },
        message: customValidation(value, imageData) || ''
      });
    }

    return rules;
  };

  const validateImages = (files: File[], imgData: ImageData[]): string[] => {
    const rules = getValidationRules();

    // Return only the first error found (most important)
    for (const rule of rules) {
      if (!rule.test(files, imgData)) {
        return [rule.message];
      }
    }

    return [];
  };

  const handleFileChange = useCallback(async (newFiles: File[]) => {
    const imgData = await loadImageData(newFiles);
    const validationErrors = validateImages(newFiles, imgData);
    setErrors(validationErrors);
    setIsValidated(true);
    onValidation?.(validationErrors);
    onChange(newFiles);
  }, [onChange, onValidation, loadImageData]);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const safeValue = Array.isArray(value) ? value : [];
    const combinedFiles = multiple ? [...safeValue, ...files] : files;
    handleFileChange(combinedFiles);
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    if (!disabled && !readOnly && dragAndDrop) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);

    if (disabled || readOnly || !dragAndDrop) return;

    const files = Array.from(event.dataTransfer.files).filter(file =>
      file.type.startsWith('image/')
    );
    const safeValue = Array.isArray(value) ? value : [];
    const combinedFiles = multiple ? [...safeValue, ...files] : files;
    handleFileChange(combinedFiles);
  };

  const removeImage = (index: number) => {
    if (disabled || readOnly || !allowRemove) return;

    const safeValue = Array.isArray(value) ? value : [];
    const newFiles = safeValue.filter((_, i) => i !== index);
    handleFileChange(newFiles);
  };

  const downloadImage = (file: File) => {
    if (!allowDownload) return;

    const url = URL.createObjectURL(file);
    const a = document.createElement('a');
    a.href = url;
    a.download = file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const openFileDialog = () => {
    if (!disabled && !readOnly) {
      fileInputRef.current?.click();
    }
  };

  const openImageModal = (url: string) => {
    if (allowZoom) {
      setSelectedImage(url);
    }
  };

  const closeImageModal = () => {
    setSelectedImage(null);
  };

  const hasErrors = errors.length > 0;
  const safeValue = Array.isArray(value) ? value : [];
  const totalSize = getTotalFileSize(safeValue);

  const getPreviewSizeClass = () => {
    switch (previewSize) {
      case 'small': return 'image-preview-small';
      case 'large': return 'image-preview-large';
      default: return 'image-preview-medium';
    }
  };

  return (
    <div className="file-input-container">
      {/* Label */}
      <label className="text-input-label" htmlFor={id}>
        {label}
        {required && <span className="required-indicator">*</span>}
        {helpText && (
          <span
            className="text-input-info-icon"
            data-tooltip={helpText}
            aria-label={helpText}
          />
        )}
      </label>

      {/* Image Input Wrapper */}
      <div className="text-input-wrapper">
        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          id={id}
          multiple={multiple}
          disabled={disabled}
          onChange={handleInputChange}
          className="hidden"
          accept="image/*"
        />

        {/* Drop Zone */}
        <div
          className={`image-drop-zone ${hasErrors ? 'has-error' : ''} ${disabled ? 'disabled' : ''} ${isDragOver ? 'drag-over' : ''} ${readOnly ? 'readonly' : ''}`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={openFileDialog}
        >
          <div className="file-drop-content">
            <ImageIcon className="file-upload-icon" size={24} />
            <p className="file-drop-text">
              {safeValue.length > 0 ? `${safeValue.length} image${safeValue.length > 1 ? 's' : ''} selected` : placeholder}
            </p>
            {dragAndDrop && !readOnly && !disabled && (
              <p className="file-drop-hint">
                Click to browse or drag and drop images here
              </p>
            )}
            {!dragAndDrop && !readOnly && !disabled && (
              <p className="file-drop-hint">
                Click to browse images
              </p>
            )}
          </div>
        </div>

        {/* Image Preview Grid */}
        {showPreview && safeValue.length > 0 && imageData.length > 0 && (
          <div className="image-preview-grid">
            {imageData.map((data, index) => (
              <div key={`${data.file.name}-${index}`} className={`image-preview-item ${getPreviewSizeClass()}`}>
                <div className="image-preview-container">
                  <img
                    src={data.url}
                    alt={data.file.name}
                    className="image-preview"
                    onClick={() => openImageModal(data.url)}
                  />

                  {/* Image Actions Overlay */}
                  <div className="image-actions-overlay">
                    {allowZoom && (
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          openImageModal(data.url);
                        }}
                        className="image-action-button"
                        title="View full size"
                      >
                        <ZoomIn size={14} />
                      </button>
                    )}
                    {allowDownload && (
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          downloadImage(data.file);
                        }}
                        className="image-action-button"
                        title="Download image"
                      >
                        <Download size={14} />
                      </button>
                    )}
                    {allowRemove && !readOnly && !disabled && (
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeImage(index);
                        }}
                        className="image-action-button remove"
                        title="Remove image"
                      >
                        <X size={14} />
                      </button>
                    )}
                  </div>
                </div>

                {/* Image Info */}
                {showImageInfo && (
                  <div className="image-info">
                    <div className="image-name">{data.file.name}</div>
                    <div className="image-meta">
                      <span className="image-size">{formatFileSize(data.file.size)}</span>
                      {showDimensions && (
                        <span className="image-dimensions">
                          {data.width} × {data.height}
                        </span>
                      )}
                      <span className="image-megapixels">
                        {data.megapixels.toFixed(1)}MP
                      </span>
                    </div>
                  </div>
                )}
              </div>
            ))}

            {/* Total Size Display */}
            {safeValue.length > 1 && (
              <div className="file-total-size">
                Total: {formatFileSize(totalSize)}
                {maxTotalSize > 0 && ` / ${formatFileSize(maxTotalSize)}`}
              </div>
            )}
          </div>
        )}

        {/* Validation Icon */}
        {isValidated && (
          <div className="validation-icon">
            {!hasErrors ? (
              <Check className="text-green-500" size={16} />
            ) : (
              <AlertCircle className="text-red-500" size={16} />
            )}
          </div>
        )}

        {/* Error Messages */}
        {hasErrors && (
          <div className="text-input-errors" role="alert" id={`${id}-error`}>
            <p className="error-message">
              {errors[0]}
            </p>
          </div>
        )}
      </div>

      {/* Image Modal */}
      {selectedImage && (
        <div className="image-modal-overlay" onClick={closeImageModal}>
          <div className="image-modal-content" onClick={(e) => e.stopPropagation()}>
            <button
              type="button"
              className="image-modal-close"
              onClick={closeImageModal}
              title="Close"
            >
              <X size={24} />
            </button>
            <img src={selectedImage} alt="Full size preview" className="image-modal-image" />
          </div>
        </div>
      )}
    </div>
  );
};

export default ThisImage;
