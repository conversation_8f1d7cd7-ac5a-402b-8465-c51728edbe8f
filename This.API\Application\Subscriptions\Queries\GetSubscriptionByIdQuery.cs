using Application.Subscriptions.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Subscriptions.Queries;

/// <summary>
/// Get Subscription by ID query
/// </summary>
public class GetSubscriptionByIdQuery : IRequest<Result<SubscriptionDto>>
{
    /// <summary>
    /// Subscription ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public GetSubscriptionByIdQuery(Guid id)
    {
        Id = id;
    }
}
