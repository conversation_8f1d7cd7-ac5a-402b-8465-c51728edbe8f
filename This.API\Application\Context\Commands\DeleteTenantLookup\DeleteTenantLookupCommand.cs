using MediatR;
using Shared.Common.Response;

namespace Application.Context.Commands.DeleteTenantLookup;

/// <summary>
/// Command to delete a tenant lookup
/// </summary>
public class DeleteTenantLookupCommand : IRequest<Result<bool>>
{
    /// <summary>
    /// TenantLookup ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteTenantLookupCommand(Guid id)
    {
        Id = id;
    }
}
