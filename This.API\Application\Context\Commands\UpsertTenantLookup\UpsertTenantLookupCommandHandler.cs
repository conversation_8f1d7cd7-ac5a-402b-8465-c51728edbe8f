using Application.Context.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Context.Commands.UpsertTenantLookup;

/// <summary>
/// Handler for UpsertTenantLookupCommand
/// </summary>
public class UpsertTenantLookupCommandHandler : IRequestHandler<UpsertTenantLookupCommand, Result<Guid>>
{
    private readonly IRepository<TenantLookup> _tenantLookupRepository;
    private readonly IRepository<TenantContext> _tenantContextRepository;
    private readonly ILogger<UpsertTenantLookupCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpsertTenantLookupCommandHandler(
        IRepository<TenantLookup> tenantLookupRepository,
        IRepository<TenantContext> tenantContextRepository,
        ILogger<UpsertTenantLookupCommandHandler> logger)
    {
        _tenantLookupRepository = tenantLookupRepository;
        _tenantContextRepository = tenantContextRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<Guid>> Handle(UpsertTenantLookupCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Validate that the tenant context exists
            var tenantContext = await _tenantContextRepository.GetByIdAsync(request.TenantContextId, cancellationToken);
            if (tenantContext == null)
            {
                return Result<Guid>.Failure($"TenantContext with ID '{request.TenantContextId}' not found.");
            }

            TenantLookup tenantLookup;

            if (request.Id.HasValue)
            {
                // Update existing tenant lookup
                tenantLookup = await _tenantLookupRepository.GetByIdAsync(request.Id.Value, cancellationToken);
                if (tenantLookup == null)
                {
                    return Result<Guid>.Failure($"TenantLookup with ID '{request.Id}' not found.");
                }

                // Update properties
                tenantLookup.Value = request.Value;
                tenantLookup.IsDefault = request.IsDefault;
                tenantLookup.Value1 = request.Value1;
                tenantLookup.Value2 = request.Value2;
                tenantLookup.ShowSequence = request.ShowSequence;
                tenantLookup.IsActive = request.IsActive;
                tenantLookup.ModifiedAt = DateTime.UtcNow;

                await _tenantLookupRepository.UpdateAsync(tenantLookup, cancellationToken);
                _logger.LogInformation("Updated tenant lookup with ID: {TenantLookupId}", tenantLookup.Id);
            }
            else
            {
                // Create new tenant lookup
                tenantLookup = new TenantLookup
                {
                    Id = Guid.NewGuid(),
                    TenantContextId = request.TenantContextId,
                    Value = request.Value,
                    IsDefault = request.IsDefault,
                    Value1 = request.Value1,
                    Value2 = request.Value2,
                    ShowSequence = request.ShowSequence,
                    IsActive = request.IsActive,
                    IsDeleted = false,
                    CreatedAt = DateTime.UtcNow
                };

                await _tenantLookupRepository.AddAsync(tenantLookup, cancellationToken);
                _logger.LogInformation("Created new tenant lookup with ID: {TenantLookupId}", tenantLookup.Id);
            }

            // If this tenant lookup is set as default, ensure no other tenant lookup in the same tenant context is default
            if (request.IsDefault)
            {
                var spec = new TenantLookupsByTenantContextIdSpec(request.TenantContextId, includeInactive: true);
                var allTenantLookups = await _tenantLookupRepository.ListAsync(spec, cancellationToken);
                
                foreach (var otherTenantLookup in allTenantLookups.Where(l => l.Id != tenantLookup.Id && l.IsDefault))
                {
                    otherTenantLookup.IsDefault = false;
                    otherTenantLookup.ModifiedAt = DateTime.UtcNow;
                    await _tenantLookupRepository.UpdateAsync(otherTenantLookup, cancellationToken);
                }
            }

            return Result<Guid>.Success(tenantLookup.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error upserting tenant lookup");
            return Result<Guid>.Failure("An error occurred while upserting the tenant lookup.");
        }
    }
}
