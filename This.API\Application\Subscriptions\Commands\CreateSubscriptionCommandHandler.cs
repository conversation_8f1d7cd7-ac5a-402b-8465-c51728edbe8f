using Application.Subscriptions.DTOs;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;
using System.Text.Json;

namespace Application.Subscriptions.Commands;

/// <summary>
/// Handler for creating subscription
/// </summary>
public class CreateSubscriptionCommandHandler : IRequestHandler<CreateSubscriptionCommand, Result<SubscriptionDto>>
{
    private readonly IRepository<Subscription> _subscriptionRepository;
    private readonly IRepository<Product> _productRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateSubscriptionCommandHandler(
        IRepository<Subscription> subscriptionRepository,
        IRepository<Product> productRepository)
    {
        _subscriptionRepository = subscriptionRepository;
        _productRepository = productRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<SubscriptionDto>> Handle(CreateSubscriptionCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Verify product exists
            var product = await _productRepository.GetByIdAsync(request.ProductId, cancellationToken);
            if (product == null)
            {
                return Result<SubscriptionDto>.Failure("Product not found.");
            }

            // Create Subscription
            var subscription = new Subscription
            {
                ProductId = request.ProductId,
                SubscriptionType = request.SubscriptionType,
                Status = request.Status,
                StartDate = request.StartDate,
                EndDate = request.EndDate,
                AutoRenew = request.AutoRenew,
                PricingTier = request.PricingTier,
                Version = request.Version,
                TemplateJson = request.TemplateJson,
                TemplateDetails = request.TemplateDetails,
                IsActive = request.IsActive
            };

            var createdSubscription = await _subscriptionRepository.AddAsync(subscription, cancellationToken);

            // Map to DTO
            var subscriptionDto = createdSubscription.Adapt<SubscriptionDto>();
            subscriptionDto.ProductName = product.Name;

            return Result<SubscriptionDto>.Success(subscriptionDto);
        }
        catch (Exception ex)
        {
            return Result<SubscriptionDto>.Failure($"Failed to create subscription: {ex.Message}");
        }
    }
}
