/* Responsive Form Layout Styles */

/* Base form grid layout */
.responsive-form-grid {
  display: grid;
  gap: 1rem;
  width: 100%;
}

/* Desktop layout: 4 columns for larger screens */
@media (min-width: 1200px) {
  .responsive-form-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }
}

/* Large tablet/small desktop: 3 columns */
@media (min-width: 1024px) and (max-width: 1199px) {
  .responsive-form-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.25rem;
  }
}

/* Tablet layout: 2 columns for medium screens */
@media (min-width: 768px) and (max-width: 1023px) {
  .responsive-form-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }
}

/* Mobile layout: 1 column for small screens */
@media (max-width: 767px) {
  .responsive-form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Form field container */
.form-field-container {
  display: flex;
  flex-direction: column;
  min-width: 0; /* Prevent overflow */
}

/* Label styling */
.form-field-container label {
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  font-size: 0.875rem;
  font-weight: 500;
  color: rgb(55, 65, 81); /* text-gray-700 */
  margin-bottom: 0.25rem;
  line-height: 1.25;
}

/* Dark mode label styling */
[data-theme="dark"] .form-field-container label {
  color: rgb(209, 213, 219); /* text-gray-300 */
}

/* Input field styling */
.form-field-container input,
.form-field-container select {
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  font-size: 0.875rem;
  line-height: 1.25rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid rgb(209, 213, 219); /* border-gray-300 */
  border-radius: 0.375rem;
  background-color: white;
  color: rgb(17, 24, 39); /* text-gray-900 */
  transition: all 0.2s ease-in-out;
  width: 100%;
  box-sizing: border-box;
}

/* Focus states */
.form-field-container input:focus,
.form-field-container select:focus {
  outline: none;
  border-color: transparent;
  ring: 2px solid rgb(59, 130, 246); /* ring-blue-500 */
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* Placeholder styling */
.form-field-container input::placeholder {
  color: rgb(156, 163, 175); /* text-gray-400 */
}

/* Dark mode input styling */
[data-theme="dark"] .form-field-container input,
[data-theme="dark"] .form-field-container select {
  background-color: rgb(31, 41, 55); /* bg-gray-800 */
  border-color: rgb(75, 85, 99); /* border-gray-600 */
  color: rgb(243, 244, 246); /* text-gray-100 */
}

[data-theme="dark"] .form-field-container input::placeholder {
  color: rgb(107, 114, 128); /* text-gray-500 */
}

/* Required indicator styling */
.form-field-container .text-red-500 {
  color: rgb(239, 68, 68); /* text-red-500 */
}

/* Select dropdown styling */
.form-field-container select {
  cursor: pointer;
  padding-right: 2rem; /* Space for dropdown arrow */
}

/* Custom dropdown arrow positioning */
.form-field-container .relative {
  position: relative;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 640px) {
  .responsive-form-grid {
    gap: 0.75rem;
  }
  
  .form-field-container input,
  .form-field-container select {
    font-size: 0.875rem;
    padding: 0.625rem 0.75rem;
  }
  
  .form-field-container label {
    font-size: 0.8125rem;
  }
}

/* Hover effects */
.form-field-container input:hover:not(:focus),
.form-field-container select:hover:not(:focus) {
  border-color: rgb(156, 163, 175); /* border-gray-400 */
}

/* Disabled state */
.form-field-container input:disabled,
.form-field-container select:disabled {
  background-color: rgb(249, 250, 251); /* bg-gray-50 */
  color: rgb(107, 114, 128); /* text-gray-500 */
  cursor: not-allowed;
  opacity: 0.6;
}

[data-theme="dark"] .form-field-container input:disabled,
[data-theme="dark"] .form-field-container select:disabled {
  background-color: rgb(55, 65, 81); /* bg-gray-700 */
  color: rgb(156, 163, 175); /* text-gray-400 */
}

/* Error state styling */
.form-field-container.has-error input,
.form-field-container.has-error select {
  border-color: rgb(239, 68, 68); /* border-red-500 */
  box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.1);
}

.form-field-container.has-error input:focus,
.form-field-container.has-error select:focus {
  ring: 2px solid rgb(239, 68, 68); /* ring-red-500 */
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.5);
}

/* Animation for smooth transitions */
.responsive-form-grid {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Ensure proper spacing in form sections */
.responsive-form-grid .form-field-container:last-child {
  margin-bottom: 0;
}

/* Accessibility improvements */
.form-field-container input:focus-visible,
.form-field-container select:focus-visible {
  outline: 2px solid rgb(59, 130, 246);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .form-field-container input,
  .form-field-container select {
    border-width: 2px;
  }
  
  .form-field-container label {
    font-weight: 600;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .form-field-container input,
  .form-field-container select,
  .responsive-form-grid {
    transition: none;
    animation: none;
  }
}
