using Domain.Entities;
using Abstraction.Database.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;
using Mapster;

namespace Application.ObjectLookup.Queries.GetAllObjectLookups;

/// <summary>
/// Handler for GetAllObjectLookupsQuery
/// </summary>
public class GetAllObjectLookupsQueryHandler : IRequestHandler<GetAllObjectLookupsQuery, Result<List<ObjectLookupDto>>>
{
    private readonly IRepository<Domain.Entities.ObjectLookup> _objectLookupRepository;
    private readonly IRepository<Domain.Entities.Object> _objectRepository;
    private readonly ILogger<GetAllObjectLookupsQueryHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetAllObjectLookupsQueryHandler(
        IRepository<Domain.Entities.ObjectLookup> objectLookupRepository,
        IRepository<Domain.Entities.Object> objectRepository,
        ILogger<GetAllObjectLookupsQueryHandler> logger)
    {
        _objectLookupRepository = objectLookupRepository;
        _objectRepository = objectRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<List<ObjectLookupDto>>> Handle(GetAllObjectLookupsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting all ObjectLookups with filters - IncludeInactive: {IncludeInactive}, SourceType: {SourceType}, SearchTerm: {SearchTerm}, ObjectId: {ObjectId}",
                request.IncludeInactive, request.SourceType, request.SearchTerm, request.ObjectId);

            // Get all ObjectLookups - tenant filtering is handled automatically by the repository
            var objectLookups = await _objectLookupRepository.ListAsync(cancellationToken);

            // Apply filters
            var filteredObjectLookups = objectLookups.AsQueryable();

            if (!request.IncludeInactive)
            {
                filteredObjectLookups = filteredObjectLookups.Where(ol => ol.IsActive);
            }

            if (!string.IsNullOrWhiteSpace(request.SourceType))
            {
                filteredObjectLookups = filteredObjectLookups.Where(ol => 
                    ol.SourceType.Contains(request.SourceType, StringComparison.OrdinalIgnoreCase));
            }

            if (!string.IsNullOrWhiteSpace(request.SearchTerm))
            {
                filteredObjectLookups = filteredObjectLookups.Where(ol => 
                    ol.Name.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase));
            }

            if (request.ObjectId.HasValue)
            {
                filteredObjectLookups = filteredObjectLookups.Where(ol => ol.ObjectId == request.ObjectId);
            }

            var resultList = filteredObjectLookups.ToList();

            // Get Object names for ObjectLookups that have ObjectId
            var objectIds = resultList.Where(ol => ol.ObjectId.HasValue).Select(ol => ol.ObjectId!.Value).Distinct().ToList();
            var objects = new Dictionary<Guid, string>();

            if (objectIds.Any())
            {
                var objectEntities = await _objectRepository.ListAsync(cancellationToken);
                objects = objectEntities.Where(o => objectIds.Contains(o.Id))
                    .ToDictionary(o => o.Id, o => o.Name);
            }

            // Map to DTOs
            var objectLookupDtos = resultList.Select(ol => 
            {
                var dto = ol.Adapt<ObjectLookupDto>();
                if (ol.ObjectId.HasValue && objects.TryGetValue(ol.ObjectId.Value, out var objectName))
                {
                    dto.ObjectName = objectName;
                }
                return dto;
            }).ToList();

            _logger.LogInformation("Successfully retrieved {Count} ObjectLookups", objectLookupDtos.Count);
            return Result<List<ObjectLookupDto>>.Success(objectLookupDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving ObjectLookups");
            return Result<List<ObjectLookupDto>>.Failure("An error occurred while retrieving ObjectLookups.");
        }
    }
}
