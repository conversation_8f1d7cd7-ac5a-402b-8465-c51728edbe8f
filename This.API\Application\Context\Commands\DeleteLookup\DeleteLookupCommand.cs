using MediatR;
using Shared.Common.Response;

namespace Application.Context.Commands.DeleteLookup;

/// <summary>
/// Command to delete a lookup
/// </summary>
public class DeleteLookupCommand : IRequest<Result<bool>>
{
    /// <summary>
    /// Lookup ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteLookupCommand(Guid id)
    {
        Id = id;
    }
}
