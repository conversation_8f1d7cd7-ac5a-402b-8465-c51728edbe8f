import React, { createContext, useContext, useCallback } from 'react';
// import { ToastContainer, toast, ToastOptions } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

export type NotificationType = 'success' | 'error' | 'warning' | 'info';

interface NotificationContextType {
  addNotification: (type: NotificationType, title: string, message: string) => void;
  dismissAll: () => void;
}

const NotificationContext = createContext<NotificationContextType>({
  addNotification: () => {},
  dismissAll: () => {}
});

export const useNotifications = () => useContext(NotificationContext);

interface NotificationProviderProps {
  children: React.ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  // Toast configuration
  // const toastConfig = {
  //   position: 'top-right',
  //   autoClose: 5000,
  //   hideProgressBar: false,
  //   closeOnClick: true,
  //   pauseOnHover: true,
  //   draggable: true,
  //   progress: undefined,
  // };

  const addNotification = useCallback((type: NotificationType, title: string, message: string) => {
    const content = (
      <div>
        <h4 className="font-medium">{title}</h4>
        <p className="text-sm">{message}</p>
      </div>
    );

    switch (type) {
      case 'success':
        console.log('Success:', content);
        break;
      case 'error':
        console.error('Error:', content);
        break;
      case 'warning':
        console.warn('Warning:', content);
        break;
      case 'info':
        console.info('Info:', content);
        break;
      default:
        console.log('Notification:', content);
    }
  }, []);

  const dismissAll = useCallback(() => {
    console.log('Clearing all notifications');
  }, []);

  return (
    <NotificationContext.Provider value={{ addNotification, dismissAll }}>
      {children}
      {/* ToastContainer placeholder */}
    </NotificationContext.Provider>
  );
};
