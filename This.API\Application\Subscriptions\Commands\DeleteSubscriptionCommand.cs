using MediatR;
using Shared.Common.Response;

namespace Application.Subscriptions.Commands;

/// <summary>
/// Delete Subscription command
/// </summary>
public class DeleteSubscriptionCommand : IRequest<Result<bool>>
{
    /// <summary>
    /// Subscription ID to delete
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteSubscriptionCommand(Guid id)
    {
        Id = id;
    }
}
