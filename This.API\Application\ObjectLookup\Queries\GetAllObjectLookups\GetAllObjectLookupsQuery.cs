using MediatR;
using Shared.Common.Response;

namespace Application.ObjectLookup.Queries.GetAllObjectLookups;

/// <summary>
/// Query to get all ObjectLookups with filtering options
/// </summary>
public class GetAllObjectLookupsQuery : IRequest<Result<List<ObjectLookupDto>>>
{
    /// <summary>
    /// Whether to include inactive ObjectLookups
    /// </summary>
    public bool IncludeInactive { get; set; } = false;

    /// <summary>
    /// Filter by source type
    /// </summary>
    public string? SourceType { get; set; }

    /// <summary>
    /// Search term to filter by name
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Filter by Object ID
    /// </summary>
    public Guid? ObjectId { get; set; }
}

/// <summary>
/// DTO for ObjectLookup data
/// </summary>
public class ObjectLookupDto
{
    /// <summary>
    /// ObjectLookup ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Name of the lookup configuration
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Source type for the lookup
    /// </summary>
    public string SourceType { get; set; } = string.Empty;

    /// <summary>
    /// Optional Object ID if source type is Object-based
    /// </summary>
    public Guid? ObjectId { get; set; }

    /// <summary>
    /// Object name (if ObjectId is set)
    /// </summary>
    public string? ObjectName { get; set; }

    /// <summary>
    /// Field to use for display text
    /// </summary>
    public string DisplayField { get; set; } = string.Empty;

    /// <summary>
    /// Field to use for the value
    /// </summary>
    public string ValueField { get; set; } = string.Empty;

    /// <summary>
    /// Metadata field to use for display text
    /// </summary>
    public string? MetadataFieldForDisplay { get; set; }

    /// <summary>
    /// Metadata field to use for the value
    /// </summary>
    public string? MetadataFieldForValue { get; set; }

    /// <summary>
    /// Whether this lookup supports tenant filtering
    /// </summary>
    public bool SupportsTenantFiltering { get; set; }

    /// <summary>
    /// Field to sort by
    /// </summary>
    public string SortBy { get; set; } = string.Empty;

    /// <summary>
    /// Sort order
    /// </summary>
    public string SortOrder { get; set; } = string.Empty;

    /// <summary>
    /// Whether the ObjectLookup is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Created date
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Modified date
    /// </summary>
    public DateTime ModifiedAt { get; set; }
}
