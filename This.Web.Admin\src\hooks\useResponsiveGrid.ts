import { useState, useEffect, useCallback } from 'react';
import type { GridA<PERSON> } from 'ag-grid-community';

interface ResponsiveGridConfig {
  minHeight?: number;
  maxHeight?: number;
  headerOffset?: number;
  footerOffset?: number;
  padding?: number;
}

interface ResponsiveGridResult {
  gridHeight: number;
  containerClass: string;
  onGridReady: (params: { api: GridApi }) => void;
  isSmallScreen: boolean;
  isMediumScreen: boolean;
}

/**
 * Custom hook for responsive ag-grid implementation
 * Calculates dynamic height based on viewport and provides responsive utilities
 */
export const useResponsiveGrid = (config: ResponsiveGridConfig = {}): ResponsiveGridResult => {
  const {
    minHeight = 300,
    maxHeight = 800,
    headerOffset = 200, // Space for page header, navigation, etc.
    footerOffset = 100, // Space for pagination, footer, etc.
    padding = 48 // Additional padding
  } = config;

  const [gridHeight, setGridHeight] = useState(600);
  const [gridApi, setGridApi] = useState<GridApi | null>(null);
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  const [isMediumScreen, setIsMediumScreen] = useState(false);

  // Calculate responsive grid height
  const calculateGridHeight = useCallback(() => {
    const viewportHeight = window.innerHeight;
    const availableHeight = viewportHeight - headerOffset - footerOffset - padding;
    
    // Clamp between min and max height
    const calculatedHeight = Math.max(minHeight, Math.min(maxHeight, availableHeight));
    setGridHeight(calculatedHeight);
  }, [minHeight, maxHeight, headerOffset, footerOffset, padding]);

  // Check screen size breakpoints
  const checkScreenSize = useCallback(() => {
    const width = window.innerWidth;
    setIsSmallScreen(width < 768);
    setIsMediumScreen(width >= 768 && width < 1024);
  }, []);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      calculateGridHeight();
      checkScreenSize();
      
      // Auto-size columns on resize if grid is ready
      if (gridApi) {
        setTimeout(() => {
          gridApi.sizeColumnsToFit();
        }, 100);
      }
    };

    // Initial calculation
    calculateGridHeight();
    checkScreenSize();

    // Add resize listener
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [calculateGridHeight, checkScreenSize, gridApi]);

  // Grid ready handler
  const onGridReady = useCallback((params: { api: GridApi }) => {
    setGridApi(params.api);
    
    // Auto-size columns when grid is ready
    setTimeout(() => {
      params.api.sizeColumnsToFit();
    }, 100);
  }, []);

  // Generate responsive container class
  const containerClass = `ag-theme-alpine ag-grid-container ${
    isSmallScreen ? 'ag-grid-mobile' : 
    isMediumScreen ? 'ag-grid-tablet' : 
    'ag-grid-desktop'
  }`;

  return {
    gridHeight,
    containerClass,
    onGridReady,
    isSmallScreen,
    isMediumScreen
  };
};
