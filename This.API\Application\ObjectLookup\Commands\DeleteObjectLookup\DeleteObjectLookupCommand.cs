using MediatR;
using Shared.Common.Response;

namespace Application.ObjectLookup.Commands.DeleteObjectLookup;

/// <summary>
/// Command to delete an ObjectLookup
/// </summary>
public class DeleteObjectLookupCommand : IRequest<Result<bool>>
{
    /// <summary>
    /// ObjectLookup ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteObjectLookupCommand(Guid id)
    {
        Id = id;
    }
}
