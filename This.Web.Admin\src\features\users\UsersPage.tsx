import { Button } from '@/shared/components/atoms/Button/Button';
import { PaginationControls } from '@/shared/components/molecules/PaginationControls/PaginationControls';
import { cn } from '@/shared/utils/utils';
import type { ColDef, GridReadyEvent } from 'ag-grid-community';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import { AgGridReact } from 'ag-grid-react';
import { AlertCircle, RefreshCw, Users } from 'lucide-react';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useResponsiveGrid } from '../../hooks/useResponsiveGrid';
import { ApiService, type User } from '../../services/apiService';

interface UsersPageProps {
  className?: string;
}

export const UsersPage: React.FC<UsersPageProps> = ({ className }) => {
  // State management
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [totalCount, setTotalCount] = useState(0);
  const [paginatedUsers, setPaginatedUsers] = useState<User[]>([]);

  // Responsive grid hook
  const { gridHeight, containerClass, onGridReady: onResponsiveGridReady, isSmallScreen } = useResponsiveGrid({
    minHeight: 300,
    maxHeight: 700,
    headerOffset: 180,
    footerOffset: 80
  });

  // Get tenant ID from localStorage (following existing pattern)
  const getCurrentTenant = (): string => {
    return localStorage.getItem('selectedTenantId') || 'kitchsync'; // fallback to default
  };

  // API service instance
  const apiService = ApiService.getInstance();

  // Fetch users data
  const fetchUsers = useCallback(async (useCache: boolean = true) => {
    try {
      setLoading(true);
      setError(null);

      const tenantId = getCurrentTenant();
      const usersData = await apiService.getUsers(tenantId, useCache);

      setUsers(usersData);
      setTotalCount(usersData.length);

      // Apply pagination to the data
      const startIndex = (currentPage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedData = usersData.slice(startIndex, endIndex);
      setPaginatedUsers(paginatedData);
    } catch (err) {
      console.error('Error fetching users:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch users');
    } finally {
      setLoading(false);
    }
  }, [apiService, currentPage, pageSize]);

  // Initial data load
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    fetchUsers(false); // Force refresh without cache
  }, [fetchUsers]);

  // Pagination handlers
  const handlePageChange = useCallback((newPage: number) => {
    setCurrentPage(newPage);
  }, []);

  const handlePageSizeChange = useCallback((newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when page size changes
  }, []);

  // Update paginated data when page or page size changes
  useEffect(() => {
    if (users.length > 0) {
      const startIndex = (currentPage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedData = users.slice(startIndex, endIndex);
      setPaginatedUsers(paginatedData);
    }
  }, [users, currentPage, pageSize]);

  // Combined grid ready handler
  const onGridReady = useCallback((params: GridReadyEvent) => {
    // Call the responsive grid handler
    onResponsiveGridReady(params);
  }, [onResponsiveGridReady]);



  // Helper function to format field names as headers
  const formatFieldName = (fieldName: string): string => {
    return fieldName
      .replace(/([A-Z])/g, ' $1') // Add space before capital letters
      .replace(/^./, str => str.toUpperCase()) // Capitalize first letter
      .trim();
  };



  // Simple dynamic column definitions - show all User properties as raw data
  const columnDefs: ColDef[] = useMemo(() => {
    if (users.length === 0) return [];

    // Get all fields from the user data
    const sampleUser = users[0];
    const allFields = Object.keys(sampleUser);

    // Generate columns for each field with minimal formatting
    const columns: ColDef[] = [];

    allFields.forEach(field => {
      const colDef: ColDef = {
        headerName: formatFieldName(field),
        field: field,
        sortable: true,
        filter: true
      };

      columns.push(colDef);
    });

    return columns;
  }, [users]);

  return (
    <div className={cn('space-y-6 p-6', className)}>
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Users className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-semibold text-foreground">Users ({users.length})</h1>
            <p className="text-sm text-muted-foreground">
              Manage and view user accounts
            </p>
          </div>
        </div>
        
        <Button
          onClick={handleRefresh}
          disabled={loading}
          variant="outline"
          className="flex items-center gap-2"
        >
          <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
          Refresh
        </Button>
      </div>

      {/* Error Alert */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertCircle className="h-4 w-4 text-red-500 mt-0.5" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-1 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

          <div className="space-y-4">
            <div className={containerClass} style={{ height: `${gridHeight}px`, width: '100%' }}>
              <AgGridReact
                rowData={paginatedUsers}
                columnDefs={columnDefs}
                onGridReady={onGridReady}
                loading={loading}

                // Grid configuration
                defaultColDef={{
                  sortable: true,
                  filter: true,
                  resizable: true,
                  minWidth: isSmallScreen ? 80 : 100
                }}

                // Disable built-in pagination
                pagination={false}

                // Other features
                animateRows={true}
                suppressCellFocus={true}
                rowSelection="multiple"

                // Responsive styling
                headerHeight={isSmallScreen ? 34 : 40}
                rowHeight={isSmallScreen ? 30 : 45}

                // Row ID for better performance
                getRowId={(params) => params.data.id}
              />
            </div>

            {/* Custom Pagination Controls */}
            <PaginationControls
              totalCount={totalCount}
              currentPage={currentPage}
              pageSize={pageSize}
              loading={loading}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
            />
          </div>
    </div>
  );
};
