import React, { useState } from 'react';
import { cn } from '@/shared/utils/utils';
import LeftNavigation from './LeftNavigation';
import { useDynamicTitle } from '../../hooks/useDynamicTitle';

interface AppLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export const AppLayout: React.FC<AppLayoutProps> = ({
  children,
  className
}) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Get dynamic title based on current route and object context
  const { title, isLoading } = useDynamicTitle();

  return (
    <div className={cn('flex h-screen bg-background', className)}>
      {/* Left Navigation Sidebar */}
      <aside className="flex-shrink-0">
        <LeftNavigation
          collapsed={sidebarCollapsed}
          onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
        />
      </aside>

      {/* Main Content Area */}
      <main className="flex-1 overflow-hidden flex flex-col">
        {/* Content Header */}
        <header className="bg-card/50 border-b border-border px-6 py-4">
          <h1 className="text-lg font-medium text-foreground">
            {isLoading ? (
              <span className="flex items-center gap-2">
                <span className="animate-pulse">Loading...</span>
              </span>
            ) : (
              title
            )}
          </h1>
        </header>

        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto">
          {children}
        </div>
      </main>
    </div>
  );
};

export default AppLayout;
