// components/ThisCurrency.tsx
import React, { useState } from 'react';
import { ChevronDown } from 'lucide-react';

// Popular currencies with their symbols and codes
const POPULAR_CURRENCIES = [
  { code: 'USD', symbol: '$', name: 'US Dollar' },
  { code: 'EUR', symbol: '€', name: 'Euro' },
  { code: 'GBP', symbol: '£', name: 'British Pound' },
  { code: 'JPY', symbol: '¥', name: 'Japanese Yen' },
  { code: 'INR', symbol: '₹', name: 'Indian Rupee' },
  { code: 'CNY', symbol: '¥', name: 'Chinese Yuan' },
  { code: 'CAD', symbol: 'C$', name: 'Canadian Dollar' },
  { code: 'AUD', symbol: 'A$', name: 'Australian Dollar' },
  { code: 'CHF', symbol: 'CHF', name: 'Swiss Franc' },
  { code: 'SEK', symbol: 'kr', name: 'Swedish Krona' },
  { code: 'NOK', symbol: 'kr', name: 'Norwegian Krone' },
  { code: 'DK<PERSON>', symbol: 'kr', name: 'Danish Krone' },
  { code: 'PLN', symbol: 'zł', name: 'Polish Zloty' },
  { code: 'CZ<PERSON>', symbol: 'Kč', name: 'Czech Koruna' },
  { code: 'HUF', symbol: 'Ft', name: 'Hungarian Forint' },
  { code: 'RUB', symbol: '₽', name: 'Russian Ruble' },
  { code: 'BRL', symbol: 'R$', name: 'Brazilian Real' },
  { code: 'MXN', symbol: '$', name: 'Mexican Peso' },
  { code: 'SGD', symbol: 'S$', name: 'Singapore Dollar' },
  { code: 'HKD', symbol: 'HK$', name: 'Hong Kong Dollar' },
  { code: 'KRW', symbol: '₩', name: 'South Korean Won' },
  { code: 'THB', symbol: '฿', name: 'Thai Baht' },
  { code: 'MYR', symbol: 'RM', name: 'Malaysian Ringgit' },
  { code: 'IDR', symbol: 'Rp', name: 'Indonesian Rupiah' },
  { code: 'PHP', symbol: '₱', name: 'Philippine Peso' },
  { code: 'VND', symbol: '₫', name: 'Vietnamese Dong' },
  { code: 'TRY', symbol: '₺', name: 'Turkish Lira' },
  { code: 'ZAR', symbol: 'R', name: 'South African Rand' },
  { code: 'EGP', symbol: '£', name: 'Egyptian Pound' },
  { code: 'AED', symbol: 'د.إ', name: 'UAE Dirham' },
  { code: 'SAR', symbol: '﷼', name: 'Saudi Riyal' },
  { code: 'QAR', symbol: '﷼', name: 'Qatari Riyal' },
  { code: 'KWD', symbol: 'د.ك', name: 'Kuwaiti Dinar' },
  { code: 'BHD', symbol: '.د.ب', name: 'Bahraini Dinar' },
  { code: 'OMR', symbol: '﷼', name: 'Omani Rial' },
  { code: 'JOD', symbol: 'د.ا', name: 'Jordanian Dinar' },
  { code: 'LBP', symbol: '£', name: 'Lebanese Pound' },
  { code: 'ILS', symbol: '₪', name: 'Israeli Shekel' },
  { code: 'PKR', symbol: '₨', name: 'Pakistani Rupee' },
  { code: 'BDT', symbol: '৳', name: 'Bangladeshi Taka' },
  { code: 'LKR', symbol: '₨', name: 'Sri Lankan Rupee' },
  { code: 'NPR', symbol: '₨', name: 'Nepalese Rupee' },
  { code: 'AFN', symbol: '؋', name: 'Afghan Afghani' },
  { code: 'IRR', symbol: '﷼', name: 'Iranian Rial' },
  { code: 'IQD', symbol: 'ع.د', name: 'Iraqi Dinar' }
];

interface ThisCurrencyProps {
  id: string;
  label: string;
  placeholder?: string;
  required?: boolean;
  min?: number;
  max?: number;
  decimals?: number;
  value: string;
  currency: string;
  onChange: (value: string) => void;
  onCurrencyChange: (currency: string) => void;
  onValidation?: (errors: string[]) => void;
  disabled?: boolean;
  readOnly?: boolean;
  helpText?: string;
  allowNegative?: boolean;
}

interface ValidationRule {
  test: (value: string) => boolean;
  message: string;
}

const ThisCurrency: React.FC<ThisCurrencyProps> = ({
  id,
  label,
  placeholder = '',
  required = false,
  min,
  max,
  decimals = 2,
  value,
  currency,
  onChange,
  onCurrencyChange,
  onValidation,
  disabled = false,
  readOnly = false,
  helpText,
  allowNegative = false
}) => {
  const [errors, setErrors] = useState<string[]>([]);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Get current currency info
  const currentCurrency = POPULAR_CURRENCIES.find(c => c.code === currency) || POPULAR_CURRENCIES[0];

  // Filter currencies based on search term
  const filteredCurrencies = POPULAR_CURRENCIES.filter(curr =>
    curr.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    curr.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Helper function to parse number from string
  const parseNumber = (val: string): number | null => {
    if (!val || val.trim() === '') return null;
    const num = parseFloat(val.replace(/,/g, ''));
    return isNaN(num) ? null : num;
  };

  // Validation rules in priority order
  const getValidationRules = (): ValidationRule[] => {
    const rules: ValidationRule[] = [];

    // 1. Required validation
    if (required) {
      rules.push({
        test: (val) => val.trim().length > 0,
        message: `${label} is required`
      });
    }

    // Only validate format if field has content
    if (required || value.trim().length > 0) {
      // 2. Number format validation
      rules.push({
        test: (val) => {
          if (!val || val.trim() === '') return true;
          const num = parseNumber(val);
          return num !== null;
        },
        message: 'Enter a valid amount'
      });

      // 3. Negative number validation
      if (!allowNegative) {
        rules.push({
          test: (val) => {
            const num = parseNumber(val);
            return num === null || num >= 0;
          },
          message: 'Negative amounts are not allowed'
        });
      }

      // 4. Minimum value validation
      if (min !== undefined) {
        rules.push({
          test: (val) => {
            const num = parseNumber(val);
            return num === null || num >= min;
          },
          message: `Minimum amount is ${currentCurrency.symbol}${min}`
        });
      }

      // 5. Maximum value validation
      if (max !== undefined) {
        rules.push({
          test: (val) => {
            const num = parseNumber(val);
            return num === null || num <= max;
          },
          message: `Maximum amount is ${currentCurrency.symbol}${max.toLocaleString()}`
        });
      }

      // 6. Decimal places validation
      if (decimals !== undefined) {
        rules.push({
          test: (val) => {
            const num = parseNumber(val);
            if (num === null) return true;

            const decimalPart = val.split('.')[1];
            return !decimalPart || decimalPart.length <= decimals;
          },
          message: `Maximum ${decimals} decimal places allowed`
        });
      }
    }

    return rules;
  };

  const validateValue = (val: string): string[] => {
    const rules = getValidationRules();

    // Return only the first error found
    for (const rule of rules) {
      if (!rule.test(val)) {
        return [rule.message];
      }
    }

    return [];
  };

  // Format number with thousands separators
  const formatNumber = (val: string): string => {
    if (!val || val.trim() === '') return val;

    const num = parseNumber(val);
    if (num === null) return val;

    // Format with appropriate decimal places
    const formatted = num.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: decimals
    });

    return formatted;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let newValue = e.target.value;
    onChange(newValue);

    // Real-time validation
    const newErrors = validateValue(newValue);
    setErrors(newErrors);
    onValidation?.(newErrors);
  };

  const handleBlur = () => {
    // Format the number on blur if it's valid
    let formattedValue = value;
    const num = parseNumber(value);

    if (num !== null && !errors.length) {
      formattedValue = formatNumber(value);
      if (formattedValue !== value) {
        onChange(formattedValue);
      }
    }

    // Validate on blur
    const newErrors = validateValue(formattedValue);
    setErrors(newErrors);
    onValidation?.(newErrors);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Allow: backspace, delete, tab, escape, enter, navigation keys
    if ([8, 9, 27, 13, 46, 35, 36, 37, 38, 39, 40].includes(e.keyCode) ||
        // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
        (e.ctrlKey && [65, 67, 86, 88].includes(e.keyCode))) {
      return;
    }

    // Allow decimal point (only one)
    if ((e.keyCode === 190 || e.keyCode === 110) && decimals > 0) {
      if (value.indexOf('.') !== -1) {
        e.preventDefault();
      }
      return;
    }

    // Allow minus sign at beginning
    if ((e.keyCode === 189 || e.keyCode === 109) && allowNegative) {
      if (value.indexOf('-') !== -1 || (e.target as HTMLInputElement).selectionStart !== 0) {
        e.preventDefault();
      }
      return;
    }

    // Only allow numbers
    if (e.shiftKey || (e.keyCode < 48 || e.keyCode > 57) && (e.keyCode < 96 || e.keyCode > 105)) {
      e.preventDefault();
    }
  };

  const handleCurrencySelect = (selectedCurrency: string) => {
    onCurrencyChange(selectedCurrency);
    setIsDropdownOpen(false);
    setSearchTerm('');
  };

  const handleDropdownToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled && !readOnly) {
      setIsDropdownOpen(!isDropdownOpen);
    }
  };

  const handleDropdownClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  const hasErrors = errors.length > 0;

  return (
    <div className="text-input-container">
      {/* Label */}
      <label htmlFor={id} className="text-input-label">
        {label}
        {required && <span className="required-indicator">*</span>}
        {helpText && (
          <span
            className="text-input-info-icon"
            data-tooltip={helpText}
            aria-label={helpText}
          />
        )}
      </label>

      {/* Currency Input Field */}
      <div className="text-input-wrapper">
        <div className={`currency-input-container ${hasErrors ? 'has-error' : ''} ${disabled ? 'disabled' : ''}`}>
          {/* Currency Dropdown */}
          <div className="currency-dropdown">
            <button
              type="button"
              className="currency-dropdown-trigger"
              onClick={handleDropdownToggle}
              disabled={disabled || readOnly}
              aria-label={`Selected currency: ${currentCurrency.code}`}
            >
              <span className="currency-code">{currentCurrency.code}</span>
              <ChevronDown className={`currency-dropdown-icon ${isDropdownOpen ? 'open' : ''}`} size={16} />
            </button>

            {/* Dropdown Menu */}
            {isDropdownOpen && (
              <div className="currency-dropdown-menu" onClick={handleDropdownClick}>
                {/* Search Input */}
                <div className="currency-search">
                  <input
                    type="text"
                    placeholder="Search currencies..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="currency-search-input"
                    autoFocus
                  />
                </div>

                {/* Currency List */}
                <div className="currency-list">
                  {filteredCurrencies.map((curr) => (
                    <button
                      key={curr.code}
                      type="button"
                      className={`currency-option ${curr.code === currency ? 'selected' : ''}`}
                      onClick={() => handleCurrencySelect(curr.code)}
                    >
                      <span className="currency-option-code">{curr.code}</span>
                      <span className="currency-option-symbol">{curr.symbol}</span>
                      <span className="currency-option-name">{curr.name}</span>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Amount Input */}
          <input
            id={id}
            type="text"
            inputMode="numeric"
            value={value}
            placeholder={placeholder || `ex. 1000000`}
            disabled={disabled}
            readOnly={readOnly}
            onChange={handleChange}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            className="currency-amount-input"
            aria-invalid={hasErrors ? 'true' : 'false'}
            aria-describedby={helpText ? `${id}-help` : undefined}
          />
        </div>

        {/* Error Message */}
        {hasErrors && (
          <div className="text-input-errors" role="alert">
            <p className="error-message">
              {errors[0]}
            </p>
          </div>
        )}
      </div>

      {/* Dropdown Backdrop */}
      {isDropdownOpen && (
        <div
          className="currency-dropdown-backdrop"
          onClick={() => setIsDropdownOpen(false)}
        />
      )}
    </div>
  );
};

export default ThisCurrency;
