// components/ThisRichText.tsx
import React, { useState, useCallback } from 'react';
import { useE<PERSON><PERSON>, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import CharacterCount from '@tiptap/extension-character-count';
import Link from '@tiptap/extension-link';
import TextStyle from '@tiptap/extension-text-style';
import { Color } from '@tiptap/extension-color';
import Highlight from '@tiptap/extension-highlight';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import TaskList from '@tiptap/extension-task-list';
import TaskItem from '@tiptap/extension-task-item';
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  Strikethrough,
  Code,
  Heading1,
  Heading2,
  Heading3,
  List,
  ListOrdered,
  Quote,
  Undo,
  Redo,
  Link as LinkIcon,
  Unlink,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,

  CheckSquare,
  Type
} from 'lucide-react';

interface ThisRichTextProps {
  id: string;
  label: string;
  value: string;
  onChange: (value: string) => void;
  onValidation?: (errors: string[]) => void;
  disabled?: boolean;
  readOnly?: boolean;
  helpText?: string;
  placeholder?: string;
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  minWords?: number;
  maxWords?: number;
  minLines?: number;
  maxLines?: number;
  allowedTags?: string[];
  blockedTags?: string[];
  allowLinks?: boolean;
  allowImages?: boolean;
  allowTables?: boolean;
  allowCodeBlocks?: boolean;
  allowLists?: boolean;
  allowHeadings?: boolean;
  allowFormatting?: boolean;
  allowColors?: boolean;
  allowHighlight?: boolean;
  allowAlignment?: boolean;
  allowTaskLists?: boolean;
  showToolbar?: boolean;
  showCharacterCount?: boolean;
  showWordCount?: boolean;
  height?: string;
  customValidation?: (value: string, textContent: string) => string | null;
}

interface ValidationRule {
  test: (value: string, textContent: string) => boolean;
  message: string;
}

const ThisRichText: React.FC<ThisRichTextProps> = ({
  id,
  label,
  value,
  onChange,
  onValidation,
  disabled = false,
  readOnly = false,
  helpText,
  placeholder = 'Start typing...',
  required = false,
  minLength = 0,
  maxLength = 10000,
  minWords = 0,
  maxWords = 0,
  minLines = 0,
  maxLines = 0,
  allowedTags,
  blockedTags,
  allowLinks = true,

  allowCodeBlocks = true,
  allowLists = true,
  allowHeadings = true,
  allowFormatting = true,
  allowColors = false,
  allowHighlight = false,
  allowAlignment = false,
  allowTaskLists = false,
  showToolbar = true,
  showCharacterCount = true,
  showWordCount = false,
  height = '200px',
  customValidation
}) => {
  const [errors, setErrors] = useState<string[]>([]);
  const [isFocused, setIsFocused] = useState(false);

  // Configure editor extensions
  const extensions: any[] = [
    StarterKit.configure({
      heading: allowHeadings ? {
        levels: [1, 2, 3]
      } : false,
      bulletList: allowLists ? {} : false,
      orderedList: allowLists ? {} : false,
      listItem: allowLists ? {} : false,
      blockquote: allowFormatting ? {} : false,
      code: allowFormatting ? {} : false,
      codeBlock: allowCodeBlocks ? {} : false,
      bold: allowFormatting ? {} : false,
      italic: allowFormatting ? {} : false,
      strike: allowFormatting ? {} : false,
    }),
    Placeholder.configure({
      placeholder,
    }),
    CharacterCount.configure({
      limit: maxLength,
    }),
  ];

  // Add conditional extensions
  if (allowLinks) {
    extensions.push(
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'rich-text-link',
        },
      })
    );
  }

  if (allowFormatting && allowColors) {
    extensions.push(TextStyle, Color);
  }

  if (allowFormatting && allowHighlight) {
    extensions.push(
      Highlight.configure({
        multicolor: true,
      })
    );
  }

  if (allowFormatting) {
    extensions.push(Underline);
  }

  if (allowAlignment) {
    extensions.push(
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      })
    );
  }

  if (allowTaskLists) {
    extensions.push(
      TaskList,
      TaskItem.configure({
        nested: true,
      })
    );
  }

  const editor = useEditor({
    extensions,
    content: value,
    editable: !disabled && !readOnly,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      onChange(html);

      // Real-time validation
      const textContent = editor.getText();
      const newErrors = validateValue(html, textContent);
      setErrors(newErrors);
      onValidation?.(newErrors);
    },
    onFocus: () => setIsFocused(true),
    onBlur: () => setIsFocused(false),
  });

  // Helper functions
  const getWordCount = (text: string): number => {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  };

  const getLineCount = (text: string): number => {
    return text.split('\n').length;
  };



  const getUsedTags = (html: string): string[] => {
    const tagRegex = /<(\w+)[^>]*>/g;
    const tags: string[] = [];
    let match;
    while ((match = tagRegex.exec(html)) !== null) {
      if (!tags.includes(match[1])) {
        tags.push(match[1]);
      }
    }
    return tags;
  };

  // Validation rules in priority order
  const getValidationRules = (): ValidationRule[] => {
    const rules: ValidationRule[] = [];

    // 1. Required validation (highest priority)
    if (required) {
      rules.push({
        test: (_val, textContent) => textContent.trim().length > 0,
        message: `${label} is required`
      });
    }

    // 2. Minimum length validation
    if (minLength > 0) {
      rules.push({
        test: (_val, textContent) => {
          if (textContent.trim() === '') return !required;
          return textContent.length >= minLength;
        },
        message: `${label} must be at least ${minLength} characters`
      });
    }

    // 3. Maximum length validation
    if (maxLength > 0) {
      rules.push({
        test: (_val, textContent) => {
          if (textContent.trim() === '') return !required;
          return textContent.length <= maxLength;
        },
        message: `${label} must be at most ${maxLength} characters`
      });
    }

    // 4. Minimum words validation
    if (minWords > 0) {
      rules.push({
        test: (_val, textContent) => {
          if (textContent.trim() === '') return !required;
          return getWordCount(textContent) >= minWords;
        },
        message: `${label} must have at least ${minWords} words`
      });
    }

    // 5. Maximum words validation
    if (maxWords > 0) {
      rules.push({
        test: (_val, textContent) => {
          if (textContent.trim() === '') return !required;
          return getWordCount(textContent) <= maxWords;
        },
        message: `${label} must have at most ${maxWords} words`
      });
    }

    // 6. Minimum lines validation
    if (minLines > 0) {
      rules.push({
        test: (_val, textContent) => {
          if (textContent.trim() === '') return !required;
          return getLineCount(textContent) >= minLines;
        },
        message: `${label} must have at least ${minLines} lines`
      });
    }

    // 7. Maximum lines validation
    if (maxLines > 0) {
      rules.push({
        test: (_val, textContent) => {
          if (textContent.trim() === '') return !required;
          return getLineCount(textContent) <= maxLines;
        },
        message: `${label} must have at most ${maxLines} lines`
      });
    }

    // 8. Allowed tags validation
    if (allowedTags && allowedTags.length > 0) {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const usedTags = getUsedTags(val);
          return usedTags.every(tag => allowedTags.includes(tag));
        },
        message: `${label} can only contain these HTML tags: ${allowedTags.join(', ')}`
      });
    }

    // 9. Blocked tags validation
    if (blockedTags && blockedTags.length > 0) {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const usedTags = getUsedTags(val);
          return !usedTags.some(tag => blockedTags.includes(tag));
        },
        message: `${label} cannot contain these HTML tags: ${blockedTags.join(', ')}`
      });
    }

    // 10. Custom validation
    if (customValidation) {
      rules.push({
        test: (val, textContent) => {
          const customError = customValidation(val, textContent);
          return customError === null;
        },
        message: customValidation(value, editor?.getText() || '') || ''
      });
    }

    return rules;
  };

  const validateValue = (val: string, textContent: string): string[] => {
    const rules = getValidationRules();

    // Return only the first error found (most important)
    for (const rule of rules) {
      if (!rule.test(val, textContent)) {
        return [rule.message];
      }
    }

    return [];
  };

  // Toolbar button handlers
  const setLink = useCallback(() => {
    if (!editor) return;

    const previousUrl = editor.getAttributes('link').href;
    const url = window.prompt('URL', previousUrl);

    if (url === null) return;

    if (url === '') {
      editor.chain().focus().extendMarkRange('link').unsetLink().run();
      return;
    }

    editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run();
  }, [editor]);

  const unsetLink = useCallback(() => {
    if (!editor) return;
    editor.chain().focus().unsetLink().run();
  }, [editor]);

  if (!editor) {
    return null;
  }

  const hasErrors = errors.length > 0;
  const characterCount = editor.storage.characterCount.characters();
  const wordCount = getWordCount(editor.getText());

  return (
    <div className="text-input-container">
      {/* Label */}
      <label className="text-input-label" htmlFor={id}>
        {label}
        {required && <span className="required-indicator">*</span>}
        {helpText && (
          <span
            className="text-input-info-icon"
            data-tooltip={helpText}
            aria-label={helpText}
          />
        )}
      </label>

      {/* Rich Text Editor */}
      <div className="text-input-wrapper">
        <div className={`rich-text-container ${hasErrors ? 'has-error' : ''} ${disabled ? 'disabled' : ''} ${isFocused ? 'focused' : ''}`}>
          {/* Toolbar */}
          {showToolbar && !readOnly && !disabled && (
            <div className="rich-text-toolbar">
              {/* Text Formatting */}
              {allowFormatting && (
                <div className="toolbar-group">
                  <button
                    type="button"
                    onClick={() => editor.chain().focus().toggleBold().run()}
                    className={`toolbar-button ${editor.isActive('bold') ? 'active' : ''}`}
                    title="Bold"
                  >
                    <Bold size={16} />
                  </button>
                  <button
                    type="button"
                    onClick={() => editor.chain().focus().toggleItalic().run()}
                    className={`toolbar-button ${editor.isActive('italic') ? 'active' : ''}`}
                    title="Italic"
                  >
                    <Italic size={16} />
                  </button>
                  <button
                    type="button"
                    onClick={() => editor.chain().focus().toggleUnderline().run()}
                    className={`toolbar-button ${editor.isActive('underline') ? 'active' : ''}`}
                    title="Underline"
                  >
                    <UnderlineIcon size={16} />
                  </button>
                  <button
                    type="button"
                    onClick={() => editor.chain().focus().toggleStrike().run()}
                    className={`toolbar-button ${editor.isActive('strike') ? 'active' : ''}`}
                    title="Strikethrough"
                  >
                    <Strikethrough size={16} />
                  </button>
                  <button
                    type="button"
                    onClick={() => editor.chain().focus().toggleCode().run()}
                    className={`toolbar-button ${editor.isActive('code') ? 'active' : ''}`}
                    title="Inline Code"
                  >
                    <Code size={16} />
                  </button>
                </div>
              )}

              {/* Headings */}
              {allowHeadings && (
                <div className="toolbar-group">
                  <button
                    type="button"
                    onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
                    className={`toolbar-button ${editor.isActive('heading', { level: 1 }) ? 'active' : ''}`}
                    title="Heading 1"
                  >
                    <Heading1 size={16} />
                  </button>
                  <button
                    type="button"
                    onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
                    className={`toolbar-button ${editor.isActive('heading', { level: 2 }) ? 'active' : ''}`}
                    title="Heading 2"
                  >
                    <Heading2 size={16} />
                  </button>
                  <button
                    type="button"
                    onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
                    className={`toolbar-button ${editor.isActive('heading', { level: 3 }) ? 'active' : ''}`}
                    title="Heading 3"
                  >
                    <Heading3 size={16} />
                  </button>
                </div>
              )}

              {/* Lists */}
              {allowLists && (
                <div className="toolbar-group">
                  <button
                    type="button"
                    onClick={() => editor.chain().focus().toggleBulletList().run()}
                    className={`toolbar-button ${editor.isActive('bulletList') ? 'active' : ''}`}
                    title="Bullet List"
                  >
                    <List size={16} />
                  </button>
                  <button
                    type="button"
                    onClick={() => editor.chain().focus().toggleOrderedList().run()}
                    className={`toolbar-button ${editor.isActive('orderedList') ? 'active' : ''}`}
                    title="Numbered List"
                  >
                    <ListOrdered size={16} />
                  </button>
                  {allowTaskLists && (
                    <button
                      type="button"
                      onClick={() => editor.chain().focus().toggleTaskList().run()}
                      className={`toolbar-button ${editor.isActive('taskList') ? 'active' : ''}`}
                      title="Task List"
                    >
                      <CheckSquare size={16} />
                    </button>
                  )}
                </div>
              )}

              {/* Alignment */}
              {allowAlignment && (
                <div className="toolbar-group">
                  <button
                    type="button"
                    onClick={() => editor.chain().focus().setTextAlign('left').run()}
                    className={`toolbar-button ${editor.isActive({ textAlign: 'left' }) ? 'active' : ''}`}
                    title="Align Left"
                  >
                    <AlignLeft size={16} />
                  </button>
                  <button
                    type="button"
                    onClick={() => editor.chain().focus().setTextAlign('center').run()}
                    className={`toolbar-button ${editor.isActive({ textAlign: 'center' }) ? 'active' : ''}`}
                    title="Align Center"
                  >
                    <AlignCenter size={16} />
                  </button>
                  <button
                    type="button"
                    onClick={() => editor.chain().focus().setTextAlign('right').run()}
                    className={`toolbar-button ${editor.isActive({ textAlign: 'right' }) ? 'active' : ''}`}
                    title="Align Right"
                  >
                    <AlignRight size={16} />
                  </button>
                  <button
                    type="button"
                    onClick={() => editor.chain().focus().setTextAlign('justify').run()}
                    className={`toolbar-button ${editor.isActive({ textAlign: 'justify' }) ? 'active' : ''}`}
                    title="Justify"
                  >
                    <AlignJustify size={16} />
                  </button>
                </div>
              )}

              {/* Links */}
              {allowLinks && (
                <div className="toolbar-group">
                  <button
                    type="button"
                    onClick={setLink}
                    className={`toolbar-button ${editor.isActive('link') ? 'active' : ''}`}
                    title="Add Link"
                  >
                    <LinkIcon size={16} />
                  </button>
                  <button
                    type="button"
                    onClick={unsetLink}
                    className="toolbar-button"
                    title="Remove Link"
                    disabled={!editor.isActive('link')}
                  >
                    <Unlink size={16} />
                  </button>
                </div>
              )}

              {/* Other formatting */}
              {allowFormatting && (
                <div className="toolbar-group">
                  <button
                    type="button"
                    onClick={() => editor.chain().focus().toggleBlockquote().run()}
                    className={`toolbar-button ${editor.isActive('blockquote') ? 'active' : ''}`}
                    title="Quote"
                  >
                    <Quote size={16} />
                  </button>
                  {allowCodeBlocks && (
                    <button
                      type="button"
                      onClick={() => editor.chain().focus().toggleCodeBlock().run()}
                      className={`toolbar-button ${editor.isActive('codeBlock') ? 'active' : ''}`}
                      title="Code Block"
                    >
                      <Type size={16} />
                    </button>
                  )}
                </div>
              )}

              {/* Undo/Redo */}
              <div className="toolbar-group">
                <button
                  type="button"
                  onClick={() => editor.chain().focus().undo().run()}
                  className="toolbar-button"
                  title="Undo"
                  disabled={!editor.can().undo()}
                >
                  <Undo size={16} />
                </button>
                <button
                  type="button"
                  onClick={() => editor.chain().focus().redo().run()}
                  className="toolbar-button"
                  title="Redo"
                  disabled={!editor.can().redo()}
                >
                  <Redo size={16} />
                </button>
              </div>
            </div>
          )}

          {/* Editor Content */}
          <div
            className="rich-text-content"
            style={{ '--rich-text-height': height } as React.CSSProperties}
          >
            <EditorContent editor={editor} />
          </div>

          {/* Character/Word Count */}
          {(showCharacterCount || showWordCount) && (
            <div className="rich-text-footer">
              {showCharacterCount && (
                <span className={`character-count ${characterCount > maxLength ? 'over-limit' : ''}`}>
                  {characterCount}/{maxLength} characters
                </span>
              )}
              {showWordCount && (
                <span className="word-count">
                  {wordCount} words
                </span>
              )}
            </div>
          )}
        </div>

        {/* Error Message */}
        {hasErrors && (
          <div className="text-input-errors" role="alert" id={`${id}-error`}>
            <p className="error-message">
              {errors[0]}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ThisRichText;
