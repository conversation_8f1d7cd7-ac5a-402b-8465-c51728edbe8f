using Ardalis.Specification;
using Domain.Entities;

namespace Application.Subscriptions.Specifications;

/// <summary>
/// Specification for counting subscriptions with filters
/// </summary>
public class SubscriptionsCountSpec : Specification<Subscription>
{
    public SubscriptionsCountSpec(
        string? searchTerm = null,
        Guid? productId = null,
        string? status = null,
        bool? isActive = null)
    {
        Query.Where(s => !s.IsDeleted);

        if (productId.HasValue)
        {
            Query.Where(s => s.ProductId == productId.Value);
        }

        if (!string.IsNullOrEmpty(status))
        {
            Query.Where(s => s.Status == status);
        }

        if (isActive.HasValue)
        {
            Query.Where(s => s.IsActive == isActive.Value);
        }

        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(s => 
                s.SubscriptionType.Contains(searchTerm) ||
                s.Product.Name.Contains(searchTerm) ||
                (s.PricingTier != null && s.PricingTier.Contains(searchTerm)));
        }

        Query.Include(s => s.Product);
    }
}
