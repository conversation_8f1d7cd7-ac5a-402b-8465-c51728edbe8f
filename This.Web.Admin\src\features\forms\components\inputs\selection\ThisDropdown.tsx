// components/ThisDropdown.tsx
import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, X, Check } from 'lucide-react';

interface DropdownOption {
  value: string;
  label: string;
  disabled?: boolean;
  description?: string;
  group?: string;
}

interface ThisDropdownProps {
  id: string;
  label: string;
  options: DropdownOption[];
  value: string | string[];
  onChange: (value: string | string[]) => void;
  onValidation?: (errors: string[]) => void;
  disabled?: boolean;
  readOnly?: boolean;
  helpText?: string;
  required?: boolean;
  multiple?: boolean;
  searchable?: boolean;
  clearable?: boolean;
  placeholder?: string;
  maxHeight?: number;
  minSelected?: number;
  maxSelected?: number;
  showSelectAll?: boolean;
  groupBy?: boolean;
  customValidation?: (value: string | string[]) => string | null;
}

interface ValidationRule {
  test: (value: string | string[]) => boolean;
  message: string;
}

const ThisDropdown: React.FC<ThisDropdownProps> = ({
  id,
  label,
  options,
  value,
  onChange,
  onValidation,
  disabled = false,
  readOnly = false,
  helpText,
  required = false,
  multiple = false,
  searchable = false,
  clearable = false,
  placeholder = 'Select an option...',
  maxHeight = 200,
  minSelected,
  maxSelected,
  showSelectAll = false,
  groupBy = false,
  customValidation
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [errors, setErrors] = useState<string[]>([]);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchable && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen, searchable]);

  // Validation rules in priority order
  const getValidationRules = (): ValidationRule[] => {
    const rules: ValidationRule[] = [];

    // 1. Required validation (highest priority)
    if (required) {
      rules.push({
        test: (val) => {
          if (multiple) {
            return Array.isArray(val) && val.length > 0;
          }
          return typeof val === 'string' && val.trim().length > 0;
        },
        message: `${label} is required`
      });
    }

    // 2. Minimum selection validation (for multiple)
    if (multiple && minSelected !== undefined) {
      rules.push({
        test: (val) => Array.isArray(val) && val.length >= minSelected,
        message: `Select at least ${minSelected} option${minSelected !== 1 ? 's' : ''}`
      });
    }

    // 3. Maximum selection validation (for multiple)
    if (multiple && maxSelected !== undefined) {
      rules.push({
        test: (val) => Array.isArray(val) && val.length <= maxSelected,
        message: `Select at most ${maxSelected} option${maxSelected !== 1 ? 's' : ''}`
      });
    }

    // 4. Valid option validation
    if (options.length > 0) {
      rules.push({
        test: (val) => {
          if (multiple) {
            if (!Array.isArray(val)) return false;
            return val.every(v => options.some(option => option.value === v));
          }
          if (!val || (typeof val === 'string' && val.trim() === '')) return !required;
          return options.some(option => option.value === val);
        },
        message: 'Please select valid option(s)'
      });
    }

    // 5. Custom validation
    if (customValidation) {
      rules.push({
        test: (val) => {
          const customError = customValidation(val);
          return customError === null;
        },
        message: customValidation(value) || ''
      });
    }

    return rules;
  };

  const validateValue = (val: string | string[]): string[] => {
    const rules = getValidationRules();

    // Return only the first error found (most important)
    for (const rule of rules) {
      if (!rule.test(val)) {
        return [rule.message];
      }
    }

    return [];
  };

  const handleToggle = () => {
    if (disabled || readOnly) return;
    setIsOpen(!isOpen);
    if (!isOpen) {
      setSearchTerm('');
    }
  };

  const handleOptionSelect = (optionValue: string) => {
    if (disabled || readOnly) return;

    let newValue: string | string[];

    if (multiple) {
      const currentValues = Array.isArray(value) ? value : [];
      if (currentValues.includes(optionValue)) {
        newValue = currentValues.filter(v => v !== optionValue);
      } else {
        newValue = [...currentValues, optionValue];
      }
    } else {
      newValue = optionValue;
      setIsOpen(false);
      setSearchTerm('');
    }

    onChange(newValue);

    // Real-time validation
    const newErrors = validateValue(newValue);
    setErrors(newErrors);
    onValidation?.(newErrors);
  };

  const handleSelectAll = () => {
    if (!multiple || disabled || readOnly) return;

    const enabledOptions = filteredOptions.filter(opt => !opt.disabled);
    const allValues = enabledOptions.map(opt => opt.value);
    const currentValues = Array.isArray(value) ? value : [];
    
    const isAllSelected = enabledOptions.every(opt => currentValues.includes(opt.value));
    const newValue = isAllSelected ? [] : allValues;

    onChange(newValue);

    // Real-time validation
    const newErrors = validateValue(newValue);
    setErrors(newErrors);
    onValidation?.(newErrors);
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (disabled || readOnly) return;

    const newValue = multiple ? [] : '';
    onChange(newValue);

    // Real-time validation
    const newErrors = validateValue(newValue);
    setErrors(newErrors);
    onValidation?.(newErrors);
  };

  const handleRemoveTag = (optionValue: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (disabled || readOnly) return;

    const currentValues = Array.isArray(value) ? value : [];
    const newValue = currentValues.filter(v => v !== optionValue);
    onChange(newValue);

    // Real-time validation
    const newErrors = validateValue(newValue);
    setErrors(newErrors);
    onValidation?.(newErrors);
  };

  // Filter options based on search term
  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
    option.value.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Group options if groupBy is enabled
  const groupedOptions = groupBy
    ? filteredOptions.reduce((groups, option) => {
        const group = option.group || 'Other';
        if (!groups[group]) groups[group] = [];
        groups[group].push(option);
        return groups;
      }, {} as Record<string, DropdownOption[]>)
    : { '': filteredOptions };

  const isSelected = (optionValue: string): boolean => {
    if (multiple) {
      return Array.isArray(value) && value.includes(optionValue);
    }
    return value === optionValue;
  };

  const getSelectedOptions = (): DropdownOption[] => {
    if (multiple) {
      const selectedValues = Array.isArray(value) ? value : [];
      return options.filter(opt => selectedValues.includes(opt.value));
    }
    const selectedOption = options.find(opt => opt.value === value);
    return selectedOption ? [selectedOption] : [];
  };

  const hasErrors = errors.length > 0;
  const selectedOptions = getSelectedOptions();

  return (
    <div className="text-input-container">
      {/* Label */}
      <label className="text-input-label">
        {label}
        {required && <span className="required-indicator">*</span>}
        {helpText && (
          <span
            className="text-input-info-icon"
            data-tooltip={helpText}
            aria-label={helpText}
          />
        )}
      </label>

      {/* Dropdown */}
      <div className="text-input-wrapper">
        <div
          ref={dropdownRef}
          className={`dropdown-container ${hasErrors ? 'has-error' : ''} ${disabled ? 'disabled' : ''} ${isOpen ? 'open' : ''}`}
        >
          {/* Trigger */}
          <div
            className="dropdown-trigger"
            onClick={handleToggle}
            role="combobox"
            aria-expanded={isOpen}
            aria-haspopup="listbox"
            aria-labelledby={`${id}-label`}
          >
            <div className="dropdown-content">
              {selectedOptions.length > 0 ? (
                multiple ? (
                  <div className="dropdown-tags">
                    {selectedOptions.map(option => (
                      <span key={option.value} className="dropdown-tag">
                        {option.label}
                        <button
                          type="button"
                          className="tag-remove"
                          onClick={(e) => handleRemoveTag(option.value, e)}
                          aria-label={`Remove ${option.label}`}
                        >
                          <X size={12} />
                        </button>
                      </span>
                    ))}
                  </div>
                ) : (
                  <span className="dropdown-selected">{selectedOptions[0].label}</span>
                )
              ) : (
                <span className="dropdown-placeholder">{placeholder}</span>
              )}
            </div>

            <div className="dropdown-actions">
              {clearable && selectedOptions.length > 0 && (
                <button
                  type="button"
                  className="dropdown-clear"
                  onClick={handleClear}
                  aria-label="Clear selection"
                >
                  <X size={16} />
                </button>
              )}
              <ChevronDown className={`dropdown-chevron ${isOpen ? 'open' : ''}`} size={16} />
            </div>
          </div>

          {/* Dropdown Menu */}
          {isOpen && (
            <div className="dropdown-menu" style={{ maxHeight: `${maxHeight}px` }}>
              {/* Search */}
              {searchable && (
                <div className="dropdown-search">
                  <input
                    ref={searchInputRef}
                    type="text"
                    className="dropdown-search-input"
                    placeholder="Search options..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onClick={(e) => e.stopPropagation()}
                  />
                </div>
              )}

              {/* Select All */}
              {multiple && showSelectAll && filteredOptions.length > 1 && (
                <div className="dropdown-select-all">
                  <button
                    type="button"
                    className="dropdown-option select-all-option"
                    onClick={handleSelectAll}
                  >
                    <div className="option-content">
                      <span className="option-label">Select All</span>
                    </div>
                  </button>
                </div>
              )}

              {/* Options */}
              <div className="dropdown-options">
                {Object.keys(groupedOptions).map(groupName => (
                  <div key={groupName} className="option-group">
                    {groupBy && groupName && (
                      <div className="group-header">{groupName}</div>
                    )}
                    {groupedOptions[groupName].map(option => (
                      <button
                        key={option.value}
                        type="button"
                        className={`dropdown-option ${isSelected(option.value) ? 'selected' : ''} ${option.disabled ? 'disabled' : ''}`}
                        onClick={() => !option.disabled && handleOptionSelect(option.value)}
                        disabled={option.disabled}
                      >
                        <div className="option-content">
                          <span className="option-label">{option.label}</span>
                          {option.description && (
                            <span className="option-description">{option.description}</span>
                          )}
                        </div>
                        {multiple && isSelected(option.value) && (
                          <Check size={16} className="option-check" />
                        )}
                      </button>
                    ))}
                  </div>
                ))}

                {filteredOptions.length === 0 && (
                  <div className="dropdown-no-options">
                    No options found
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Error Message */}
        {hasErrors && (
          <div className="text-input-errors" role="alert" id={`${id}-error`}>
            <p className="error-message">
              {errors[0]}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ThisDropdown;
