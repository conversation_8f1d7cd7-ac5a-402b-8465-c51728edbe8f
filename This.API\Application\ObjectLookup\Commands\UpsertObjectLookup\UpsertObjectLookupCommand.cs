using MediatR;
using Shared.Common.Response;

namespace Application.ObjectLookup.Commands.UpsertObjectLookup;

/// <summary>
/// Command to create or update an ObjectLookup
/// </summary>
public class UpsertObjectLookupCommand : IRequest<Result<Guid>>
{
    /// <summary>
    /// ObjectLookup ID (null for create, value for update)
    /// </summary>
    public Guid? Id { get; set; }

    /// <summary>
    /// Name of the lookup configuration
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Source type for the lookup (e.g., "Name Of the Object")
    /// </summary>
    public string SourceType { get; set; } = string.Empty;

    /// <summary>
    /// Optional Object ID if source type is Object-based
    /// </summary>
    public Guid? ObjectId { get; set; }

    /// <summary>
    /// Field to use for display text (default: "Name")
    /// </summary>
    public string DisplayField { get; set; } = "Name";

    /// <summary>
    /// Field to use for the value (default: "Id")
    /// </summary>
    public string ValueField { get; set; } = "Id";

    /// <summary>
    /// Metadata field to use for display text (alternative to DisplayField)
    /// </summary>
    public string? MetadataFieldForDisplay { get; set; }

    /// <summary>
    /// Metadata field to use for the value (alternative to ValueField)
    /// </summary>
    public string? MetadataFieldForValue { get; set; }

    /// <summary>
    /// Whether this lookup supports tenant filtering
    /// </summary>
    public bool SupportsTenantFiltering { get; set; } = true;

    /// <summary>
    /// Field to sort by (default: "Name")
    /// </summary>
    public string SortBy { get; set; } = "Name";

    /// <summary>
    /// Sort order (default: "ASC")
    /// </summary>
    public string SortOrder { get; set; } = "ASC";

    /// <summary>
    /// Whether the ObjectLookup is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
