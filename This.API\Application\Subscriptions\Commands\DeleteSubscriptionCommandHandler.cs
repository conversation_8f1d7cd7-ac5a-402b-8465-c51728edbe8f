using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.Subscriptions.Commands;

/// <summary>
/// Handler for deleting subscription
/// </summary>
public class DeleteSubscriptionCommandHandler : IRequestHandler<DeleteSubscriptionCommand, Result<bool>>
{
    private readonly IRepository<Subscription> _subscriptionRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteSubscriptionCommandHandler(IRepository<Subscription> subscriptionRepository)
    {
        _subscriptionRepository = subscriptionRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<bool>> Handle(DeleteSubscriptionCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Get existing subscription
            var subscription = await _subscriptionRepository.GetByIdAsync(request.Id, cancellationToken);
            if (subscription == null)
            {
                return Result<bool>.Failure("Subscription not found.");
            }

            // Soft delete the subscription
            await _subscriptionRepository.DeleteAsync(subscription, cancellationToken);

            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            return Result<bool>.Failure($"Failed to delete subscription: {ex.Message}");
        }
    }
}
