using Ardalis.Specification;
using Domain.Entities;

namespace Application.Subscriptions.Specifications;

/// <summary>
/// Specification to get subscriptions by status
/// </summary>
public class SubscriptionByStatusSpec : Specification<Subscription>
{
    public SubscriptionByStatusSpec(string status)
    {
        Query.Where(s => s.Status == status && !s.IsDeleted)
             .Include(s => s.Product);
    }
}
