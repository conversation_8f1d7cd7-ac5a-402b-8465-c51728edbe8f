import { primaryClient } from './httpClient';
import type {
  Context,
  Lookup,
  Context<PERSON>ithLookups,
  CreateContextRequest,
  UpdateContextRequest,
  CreateLookupRequest,
  UpdateLookupRequest,
  BulkLookupRequest,
  BulkLookupResponse,
  TenantContext,
  TenantLookup,
  TenantContextWithLookups,
  CreateTenantContextRequest,
  UpdateTenantContextRequest,
  CreateTenantLookupRequest,
  UpdateTenantLookupRequest,
  ObjectLookup,
  CreateObjectLookupRequest,
  UpdateObjectLookupRequest
} from '../types/context';

// Context Service
export const contextService = {
  // Context operations
  async getAllContexts(includeInactive = false, category?: string, searchTerm?: string): Promise<Context[]> {
    const params = new URLSearchParams();
    if (includeInactive) params.append('includeInactive', 'true');
    if (category) params.append('category', category);
    if (searchTerm) params.append('searchTerm', searchTerm);

    const response = await primaryClient.get(`/context/get-all?${params.toString()}`);
    return response.data;
  },

  async getPagedContexts(
    page = 1,
    pageSize = 10,
    includeInactive = false,
    category?: string,
    searchTerm?: string
  ) {
    const params = new URLSearchParams({
      page: page.toString(),
      pageSize: pageSize.toString(),
      includeInactive: includeInactive.toString(),
    });
    if (category) params.append('category', category);
    if (searchTerm) params.append('searchTerm', searchTerm);

    const response = await primaryClient.get(`/context/get-paged?${params.toString()}`);
    return response.data;
  },

  async createContext(data: CreateContextRequest): Promise<{ id: string; message: string }> {
    const response = await primaryClient.post('/context/upsert-single', data);
    return response.data;
  },

  async updateContext(data: UpdateContextRequest): Promise<{ id: string; message: string }> {
    const response = await primaryClient.post('/context/upsert-single', data);
    return response.data;
  },

  async getContextWithLookups(contextId: string, includeInactiveLookups = false): Promise<ContextWithLookups> {
    const params = includeInactiveLookups ? '?includeInactiveLookups=true' : '';
    const response = await primaryClient.get(`/context/${contextId}/with-lookups${params}`);
    return response.data;
  },

  // Lookup operations
  async createLookup(data: CreateLookupRequest): Promise<{ id: string; message: string }> {
    const response = await primaryClient.post('/context/lookup/upsert', data);
    return response.data;
  },

  async updateLookup(data: UpdateLookupRequest): Promise<{ id: string; message: string }> {
    const response = await primaryClient.post('/context/lookup/upsert', data);
    return response.data;
  },

  async deleteLookup(lookupId: string): Promise<{ message: string }> {
    const response = await primaryClient.delete(`/context/lookup/${lookupId}`);
    return response.data;
  },

  async bulkUpsertLookups(data: BulkLookupRequest): Promise<BulkLookupResponse> {
    const response = await primaryClient.post('/context/lookup/bulk-upsert', data);
    return response.data;
  },

  // Utility methods
  async getContextCategories(): Promise<string[]> {
    const contexts = await this.getAllContexts(true);
    const categories = [...new Set(contexts.map(c => c.category).filter(Boolean))];
    return categories.sort();
  },

  // TenantContext operations
  async getAllTenantContexts(includeInactive = false, category?: string, searchTerm?: string, tenantId?: string): Promise<TenantContext[]> {
    const params = new URLSearchParams();
    if (includeInactive) params.append('includeInactive', 'true');
    if (category) params.append('category', category);
    if (searchTerm) params.append('searchTerm', searchTerm);

    const config = tenantId ? { tenant: tenantId } : {};
    const response = await primaryClient.get(`/context/tenant/get-all?${params.toString()}`, config);
    return response.data;
  },

  async createTenantContext(data: CreateTenantContextRequest, tenantId?: string): Promise<{ id: string; message: string }> {
    const config = tenantId ? { tenant: tenantId } : {};
    const response = await primaryClient.post('/context/tenant/upsert-single', data, config);
    return response.data;
  },

  async updateTenantContext(data: UpdateTenantContextRequest, tenantId?: string): Promise<{ id: string; message: string }> {
    const config = tenantId ? { tenant: tenantId } : {};
    const response = await primaryClient.post('/context/tenant/upsert-single', data, config);
    return response.data;
  },

  async getTenantContextWithLookups(tenantContextId: string, includeInactiveLookups = false, tenantId?: string): Promise<TenantContextWithLookups> {
    const params = includeInactiveLookups ? '?includeInactiveLookups=true' : '';
    const config = tenantId ? { tenant: tenantId } : {};
    const response = await primaryClient.get(`/context/tenant/${tenantContextId}/with-lookups${params}`, config);
    return response.data;
  },

  async getTenantContextCategories(tenantId?: string): Promise<string[]> {
    const tenantContexts = await this.getAllTenantContexts(true, undefined, undefined, tenantId);
    const categories = [...new Set(tenantContexts.map(tc => tc.category).filter(Boolean))];
    return categories.sort();
  },

  async getTenantContext(category?: string, searchTerm?: string, includeInactive = false, tenantId?: string): Promise<TenantContext[]> {
    const params = new URLSearchParams();
    if (includeInactive) params.append('includeInactive', 'true');
    if (category) params.append('category', category);
    if (searchTerm) params.append('searchTerm', searchTerm);

    const config = tenantId ? { tenant: tenantId } : {};
    const response = await primaryClient.get(`/context/get-tenant-context?${params.toString()}`, config);
    return response.data;
  },

  // TenantLookup operations (Note: These would need new API endpoints to be created)
  async createTenantLookup(data: CreateTenantLookupRequest, tenantId?: string): Promise<{ id: string; message: string }> {
    const config = tenantId ? { tenant: tenantId } : {};
    const response = await primaryClient.post('/context/tenant/lookup/upsert', data, config);
    return response.data;
  },

  async updateTenantLookup(data: UpdateTenantLookupRequest, tenantId?: string): Promise<{ id: string; message: string }> {
    const config = tenantId ? { tenant: tenantId } : {};
    const response = await primaryClient.post('/context/tenant/lookup/upsert', data, config);
    return response.data;
  },

  async deleteTenantLookup(lookupId: string, tenantId?: string): Promise<{ message: string }> {
    const config = tenantId ? { tenant: tenantId } : {};
    const response = await primaryClient.delete(`/context/tenant/lookup/${lookupId}`, config);
    return response.data;
  },

  // ObjectLookup operations (Note: These would need new API endpoints to be created)
  async getAllObjectLookups(includeInactive = false, sourceType?: string, searchTerm?: string, tenantId?: string): Promise<ObjectLookup[]> {
    const params = new URLSearchParams();
    if (includeInactive) params.append('includeInactive', 'true');
    if (sourceType) params.append('sourceType', sourceType);
    if (searchTerm) params.append('searchTerm', searchTerm);

    const config = tenantId ? { tenant: tenantId } : {};
    const response = await primaryClient.get(`/context/object-lookup/get-all?${params.toString()}`, config);
    return response.data;
  },

  async createObjectLookup(data: CreateObjectLookupRequest, tenantId?: string): Promise<{ id: string; message: string }> {
    const config = tenantId ? { tenant: tenantId } : {};
    const response = await primaryClient.post('/context/object-lookup/upsert', data, config);
    return response.data;
  },

  async updateObjectLookup(data: UpdateObjectLookupRequest, tenantId?: string): Promise<{ id: string; message: string }> {
    const config = tenantId ? { tenant: tenantId } : {};
    const response = await primaryClient.post('/context/object-lookup/upsert', data, config);
    return response.data;
  },

  async deleteObjectLookup(objectLookupId: string, tenantId?: string): Promise<{ message: string }> {
    const config = tenantId ? { tenant: tenantId } : {};
    const response = await primaryClient.delete(`/context/object-lookup/${objectLookupId}`, config);
    return response.data;
  },

  async getObjectLookupSourceTypes(tenantId?: string): Promise<string[]> {
    const objectLookups = await this.getAllObjectLookups(true, undefined, undefined, tenantId);
    const sourceTypes = [...new Set(objectLookups.map(ol => ol.sourceType).filter(Boolean))];
    return sourceTypes.sort();
  },

  async getObjects(searchTerm?: string, tenantId?: string): Promise<{ data: ObjectDto[]; totalCount: number }> {
    const params = new URLSearchParams();
    params.append('pageNumber', '1');
    params.append('pageSize', '100');
    params.append('isActive', 'true');
    if (searchTerm) params.append('searchTerm', searchTerm);

    const config = tenantId ? { tenant: tenantId } : {};
    const response = await primaryClient.get(`/api/objects?${params.toString()}`, config);

    // Handle PaginatedResult<ObjectDto> response structure
    return {
      data: response.data.data || [],
      totalCount: response.data.totalItems || 0
    };
  }
};
