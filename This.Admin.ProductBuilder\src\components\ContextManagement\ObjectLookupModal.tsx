import React, { useState, useEffect } from 'react';
import { Modal, Button, Form, Alert, Row, Col } from 'react-bootstrap';
import { contextService } from '../../services/contextService';
import type { ObjectLookup, CreateObjectLookupRequest, UpdateObjectLookupRequest, ObjectDto } from '../../types/context';

interface ObjectLookupModalProps {
  show: boolean;
  onHide: () => void;
  objectLookup: ObjectLookup | null;
  onSaved: () => void;
  tenantId: string;
}

interface FormData {
  name: string;
  sourceType: string;
  objectId: string;
  displayField: string;
  valueField: string;
  metadataFieldForDisplay: string;
  metadataFieldForValue: string;
  supportsTenantFiltering: boolean;
  sortBy: string;
  sortOrder: string;
  isActive: boolean;
}

export const ObjectLookupModal: React.FC<ObjectLookupModalProps> = ({
  show,
  onHide,
  objectLookup,
  onSaved,
  tenantId
}) => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    sourceType: '',
    objectId: '',
    displayField: '',
    valueField: '',
    metadataFieldForDisplay: '',
    metadataFieldForValue: '',
    supportsTenantFiltering: false,
    sortBy: '',
    sortOrder: 'ASC',
    isActive: true
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validated, setValidated] = useState(false);
  const [objects, setObjects] = useState<ObjectDto[]>([]);
  const [objectsLoading, setObjectsLoading] = useState(false);

  // Load objects for dropdown
  useEffect(() => {
    if (show && tenantId && tenantId.trim() !== '') {
      loadObjects();
    } else if (show) {
      setObjects([]);
      setError('Please select a tenant to load objects');
    }
  }, [show, tenantId]);

  // Initialize form data when modal opens
  useEffect(() => {
    if (show) {
      if (objectLookup) {
        // Edit mode
        setFormData({
          name: objectLookup.name,
          sourceType: objectLookup.sourceType,
          objectId: objectLookup.objectId || '',
          displayField: objectLookup.displayField,
          valueField: objectLookup.valueField,
          metadataFieldForDisplay: objectLookup.metadataFieldForDisplay || '',
          metadataFieldForValue: objectLookup.metadataFieldForValue || '',
          supportsTenantFiltering: objectLookup.supportsTenantFiltering,
          sortBy: objectLookup.sortBy,
          sortOrder: objectLookup.sortOrder,
          isActive: objectLookup.isActive
        });
      } else {
        // Create mode
        setFormData({
          name: '',
          sourceType: '',
          objectId: '',
          displayField: '',
          valueField: '',
          metadataFieldForDisplay: '',
          metadataFieldForValue: '',
          supportsTenantFiltering: false,
          sortBy: '',
          sortOrder: 'ASC',
          isActive: true
        });
      }
      setValidated(false);
      setError(null);
    }
  }, [show, objectLookup]);

  const loadObjects = async () => {
    try {
      setObjectsLoading(true);
      const response = await contextService.getObjects(undefined, tenantId);
      setObjects(response.data || []);
    } catch (err) {
      console.error('Error loading objects:', err);
      setError(`Failed to load objects: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setObjectsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    e.stopPropagation();

    const form = e.currentTarget;
    if (!form.checkValidity()) {
      setValidated(true);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      if (objectLookup) {
        // Update existing ObjectLookup
        const updateData: UpdateObjectLookupRequest = {
          id: objectLookup.id,
          ...formData,
          objectId: formData.objectId || undefined
        };
        await contextService.updateObjectLookup(updateData, tenantId);
      } else {
        // Create new ObjectLookup
        const createData: CreateObjectLookupRequest = {
          ...formData,
          objectId: formData.objectId || undefined
        };
        await contextService.createObjectLookup(createData, tenantId);
      }

      onSaved();
    } catch (err: any) {
      setError(err.response?.data?.message || err.message || 'Failed to save ObjectLookup');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onHide();
    }
  };

  return (
    <Modal show={show} onHide={handleClose} size="lg">
      <Modal.Header closeButton>
        <Modal.Title>
          {objectLookup ? 'Edit ObjectLookup' : 'Create ObjectLookup'}
        </Modal.Title>
      </Modal.Header>

      <Form noValidate validated={validated} onSubmit={handleSubmit}>
        <Modal.Body>
          {error && (
            <Alert variant="danger" dismissible onClose={() => setError(null)}>
              {error}
            </Alert>
          )}

          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Name <span className="text-danger">*</span></Form.Label>
                <Form.Control
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  disabled={loading}
                  placeholder="Enter ObjectLookup name"
                />
                <Form.Control.Feedback type="invalid">
                  Please provide a valid name.
                </Form.Control.Feedback>
              </Form.Group>
            </Col>

            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Source Type <span className="text-danger">*</span></Form.Label>
                <Form.Control
                  type="text"
                  name="sourceType"
                  value={formData.sourceType}
                  onChange={handleInputChange}
                  required
                  disabled={loading}
                  placeholder="Enter source type"
                />
                <Form.Control.Feedback type="invalid">
                  Please provide a valid source type.
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Object</Form.Label>
                <Form.Select
                  name="objectId"
                  value={formData.objectId}
                  onChange={handleInputChange}
                  disabled={loading || objectsLoading}
                >
                  {objectsLoading ? (
                    <option value="">Loading objects...</option>
                  ) : objects.length === 0 ? (
                    <option value="">No objects available</option>
                  ) : (
                    <>
                      <option value="">Select an object (optional)</option>
                      {objects.map(obj => (
                        <option key={obj.id} value={obj.id}>
                          {obj.name}
                        </option>
                      ))}
                    </>
                  )}
                </Form.Select>
              </Form.Group>
            </Col>

            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Supports Tenant Filtering</Form.Label>
                <Form.Check
                  type="checkbox"
                  name="supportsTenantFiltering"
                  checked={formData.supportsTenantFiltering}
                  onChange={handleInputChange}
                  disabled={loading}
                  label="Enable tenant filtering"
                />
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Display Field</Form.Label>
                <Form.Control
                  type="text"
                  name="displayField"
                  value={formData.displayField}
                  onChange={handleInputChange}
                  disabled={loading}
                  placeholder="Enter display field"
                />
              </Form.Group>
            </Col>

            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Value Field</Form.Label>
                <Form.Control
                  type="text"
                  name="valueField"
                  value={formData.valueField}
                  onChange={handleInputChange}
                  disabled={loading}
                  placeholder="Enter value field"
                />
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Metadata Field for Display</Form.Label>
                <Form.Control
                  type="text"
                  name="metadataFieldForDisplay"
                  value={formData.metadataFieldForDisplay}
                  onChange={handleInputChange}
                  disabled={loading}
                  placeholder="Enter metadata field for display"
                />
              </Form.Group>
            </Col>

            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Metadata Field for Value</Form.Label>
                <Form.Control
                  type="text"
                  name="metadataFieldForValue"
                  value={formData.metadataFieldForValue}
                  onChange={handleInputChange}
                  disabled={loading}
                  placeholder="Enter metadata field for value"
                />
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Sort By</Form.Label>
                <Form.Control
                  type="text"
                  name="sortBy"
                  value={formData.sortBy}
                  onChange={handleInputChange}
                  disabled={loading}
                  placeholder="Enter sort field"
                />
              </Form.Group>
            </Col>

            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Sort Order</Form.Label>
                <Form.Select
                  name="sortOrder"
                  value={formData.sortOrder}
                  onChange={handleInputChange}
                  disabled={loading}
                >
                  <option value="ASC">Ascending</option>
                  <option value="DESC">Descending</option>
                </Form.Select>
              </Form.Group>
            </Col>

            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Status</Form.Label>
                <Form.Check
                  type="checkbox"
                  name="isActive"
                  checked={formData.isActive}
                  onChange={handleInputChange}
                  disabled={loading}
                  label="Active"
                />
              </Form.Group>
            </Col>
          </Row>
        </Modal.Body>

        <Modal.Footer>
          <Button variant="secondary" onClick={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button variant="primary" type="submit" disabled={loading}>
            {loading ? 'Saving...' : (objectLookup ? 'Update' : 'Create')}
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};
