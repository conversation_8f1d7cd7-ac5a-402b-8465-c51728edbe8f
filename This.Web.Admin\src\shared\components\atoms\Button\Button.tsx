import React from 'react'
import { cn } from '@/shared/utils/utils'

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'default', size = 'default', asChild = false, ...props }, ref) => {
    const baseClasses = cn(
      'inline-flex items-center justify-center whitespace-nowrap rounded-lg text-xs font-normal ring-offset-background transition-all duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 font-sans relative overflow-hidden group',
      {
        // Enhanced Variants with better styling
        'bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-105 hover:shadow-lg active:scale-95 shadow-md': variant === 'default',
        'bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-105 hover:shadow-lg active:scale-95 shadow-md': variant === 'destructive',
        'border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-primary/50 hover:shadow-md active:scale-95': variant === 'outline',
        'bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-105 hover:shadow-md active:scale-95': variant === 'secondary',
        'hover:bg-accent hover:text-accent-foreground hover:scale-105 active:scale-95': variant === 'ghost',
        'text-primary underline-offset-4 hover:underline hover:text-primary/80': variant === 'link',

        // Enhanced Sizes
        'h-10 px-4 py-2 text-xs': size === 'default',
        'h-9 rounded-md px-3 text-xxs': size === 'sm',
        'h-12 rounded-lg px-8 text-sm': size === 'lg',
        'h-10 w-10': size === 'icon',
      },
      className
    )

    return (
      <button
        className={baseClasses}
        ref={ref}
        {...props}
      >
        {/* Subtle shine effect for default and destructive variants */}
        {(variant === 'default' || variant === 'destructive') && (
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700 ease-in-out" />
        )}
        {props.children}
      </button>
    )
  }
)

Button.displayName = 'Button'

export { Button }
