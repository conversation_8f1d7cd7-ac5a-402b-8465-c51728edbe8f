using Application.Subscriptions.DTOs;
using Application.Subscriptions.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.Subscriptions.Queries;

/// <summary>
/// Handler for getting subscription by ID
/// </summary>
public class GetSubscriptionByIdQueryHandler : IRequestHandler<GetSubscriptionByIdQuery, Result<SubscriptionDto>>
{
    private readonly IReadRepository<Subscription> _subscriptionRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetSubscriptionByIdQueryHandler(IReadRepository<Subscription> subscriptionRepository)
    {
        _subscriptionRepository = subscriptionRepository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<SubscriptionDto>> Handle(GetSubscriptionByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            // Get subscription using specification
            var spec = new SubscriptionByIdSpec(request.Id);
            var subscription = await _subscriptionRepository.GetBySpecAsync(spec, cancellationToken);
            if (subscription == null)
            {
                return Result<SubscriptionDto>.Failure("Subscription not found.");
            }

            // Map to DTO
            var subscriptionDto = subscription.Adapt<SubscriptionDto>();
            subscriptionDto.ProductName = subscription.Product?.Name;
            subscriptionDto.IsActive = subscription.IsActive && !subscription.IsDeleted;

            return Result<SubscriptionDto>.Success(subscriptionDto);
        }
        catch (Exception ex)
        {
            return Result<SubscriptionDto>.Failure($"Failed to get subscription: {ex.Message}");
        }
    }
}
