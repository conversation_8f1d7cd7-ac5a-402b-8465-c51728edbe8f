// components/ThisRadio.tsx
import React, { useState } from 'react';

interface RadioOption {
  value: string;
  label: string;
  disabled?: boolean;
  description?: string;
}

interface ThisRadioProps {
  id: string;
  label: string;
  options: RadioOption[];
  value: string;
  onChange: (value: string) => void;
  onValidation?: (errors: string[]) => void;
  disabled?: boolean;
  readOnly?: boolean;
  helpText?: string;
  required?: boolean;
  layout?: 'vertical' | 'horizontal' | 'grid';
  gridColumns?: number;
  showDescriptions?: boolean;
  customValidation?: (value: string) => string | null;
}

interface ValidationRule {
  test: (value: string) => boolean;
  message: string;
}

const ThisRadio: React.FC<ThisRadioProps> = ({
  id,
  label,
  options,
  value,
  onChange,
  onValidation,
  disabled = false,
  readOnly = false,
  helpText,
  required = false,
  layout = 'vertical',
  gridColumns = 2,
  showDescriptions = false,
  customValidation
}) => {
  const [errors, setErrors] = useState<string[]>([]);

  // Validation rules in priority order
  const getValidationRules = (): ValidationRule[] => {
    const rules: ValidationRule[] = [];

    // 1. Required validation (highest priority)
    if (required) {
      rules.push({
        test: (val) => val.trim().length > 0,
        message: `${label} is required`
      });
    }

    // 2. Valid option validation
    if (options.length > 0) {
      rules.push({
        test: (val) => {
          if (!val || val.trim() === '') return !required;
          return options.some(option => option.value === val);
        },
        message: 'Please select a valid option'
      });
    }

    // 3. Custom validation
    if (customValidation) {
      rules.push({
        test: (val) => {
          const customError = customValidation(val);
          return customError === null;
        },
        message: customValidation(value) || ''
      });
    }

    return rules;
  };

  const validateValue = (val: string): string[] => {
    const rules = getValidationRules();

    // Return only the first error found (most important)
    for (const rule of rules) {
      if (!rule.test(val)) {
        return [rule.message];
      }
    }

    return [];
  };

  const handleChange = (optionValue: string) => {
    if (disabled || readOnly) return;

    onChange(optionValue);

    // Real-time validation
    const newErrors = validateValue(optionValue);
    setErrors(newErrors);
    onValidation?.(newErrors);
  };

  const isChecked = (optionValue: string): boolean => {
    return value === optionValue;
  };

  const hasErrors = errors.length > 0;

  // Get layout classes
  const getLayoutClasses = (): string => {
    switch (layout) {
      case 'horizontal':
        return 'radio-options-horizontal';
      case 'grid':
        return `radio-options-grid radio-grid-${gridColumns}`;
      default:
        return 'radio-options-vertical';
    }
  };

  return (
    <div className="text-input-container">
      {/* Label */}
      <label className="text-input-label">
        {label}
        {required && <span className="required-indicator">*</span>}
        {helpText && (
          <span
            className="text-input-info-icon"
            data-tooltip={helpText}
            aria-label={helpText}
          />
        )}
      </label>

      {/* Radio Options */}
      <div className="text-input-wrapper">
        <div className={`radio-input-container ${hasErrors ? 'has-error' : ''} ${disabled ? 'disabled' : ''}`}>
          <div className={getLayoutClasses()} role="radiogroup" aria-labelledby={`${id}-label`}>
            {options.map((option) => (
              <div key={option.value} className="radio-option">
                <label className="radio-label">
                  <div className="radio-wrapper">
                    <input
                      type="radio"
                      name={id}
                      value={option.value}
                      checked={isChecked(option.value)}
                      onChange={() => handleChange(option.value)}
                      disabled={disabled || readOnly || option.disabled}
                      className="radio-input"
                      aria-describedby={hasErrors ? `${id}-error` : undefined}
                    />
                    <div className={`radio-custom ${isChecked(option.value) ? 'checked' : ''} ${option.disabled ? 'disabled' : ''}`}>
                      {isChecked(option.value) && <div className="radio-dot" />}
                    </div>
                  </div>
                  <div className="radio-content">
                    <span className={`radio-text ${option.disabled ? 'disabled' : ''}`}>
                      {option.label}
                    </span>
                    {showDescriptions && option.description && (
                      <span className="radio-description">
                        {option.description}
                      </span>
                    )}
                  </div>
                </label>
              </div>
            ))}
          </div>

          {/* Selection Display */}
          {value && (
            <div className="radio-selection-display">
              <span className="selection-display-text">
                Selected: {options.find(opt => opt.value === value)?.label || value}
              </span>
            </div>
          )}
        </div>

        {/* Error Message */}
        {hasErrors && (
          <div className="text-input-errors" role="alert" id={`${id}-error`}>
            <p className="error-message">
              {errors[0]}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ThisRadio;
