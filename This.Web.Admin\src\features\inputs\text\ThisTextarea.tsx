// components/ThisTextarea.tsx
import React, { useState, useRef, useEffect } from 'react';

interface ThisTextareaProps {
  id: string;
  label: string;
  value: string;
  onChange: (value: string) => void;
  onValidation?: (errors: string[]) => void;
  disabled?: boolean;
  readOnly?: boolean;
  helpText?: string;
  placeholder?: string;
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  minWords?: number;
  maxWords?: number;
  minLines?: number;
  maxLines?: number;
  rows?: number;
  cols?: number;
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
  autoResize?: boolean;
  showCharacterCount?: boolean;
  showWordCount?: boolean;
  showLineCount?: boolean;
  pattern?: string;
  customValidation?: (value: string) => string | null;
}

interface ValidationRule {
  test: (value: string) => boolean;
  message: string;
}

const ThisTextarea: React.FC<ThisTextareaProps> = ({
  id,
  label,
  value,
  onChange,
  onValidation,
  disabled = false,
  readOnly = false,
  helpText,
  placeholder,
  required = false,
  minLength,
  maxLength,
  minWords,
  maxWords,
  minLines,
  maxLines,
  rows = 4,
  cols,
  resize = 'vertical',
  autoResize = false,
  showCharacterCount = false,
  showWordCount = false,
  showLineCount = false,
  pattern,
  customValidation
}) => {
  const [errors, setErrors] = useState<string[]>([]);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize functionality
  useEffect(() => {
    if (autoResize && textareaRef.current) {
      const textarea = textareaRef.current;
      textarea.style.height = 'auto';
      textarea.style.height = `${textarea.scrollHeight}px`;
    }
  }, [value, autoResize]);

  // Validation rules in priority order
  const getValidationRules = (): ValidationRule[] => {
    const rules: ValidationRule[] = [];

    // 1. Required validation (highest priority)
    if (required) {
      rules.push({
        test: (val) => val.trim().length > 0,
        message: `${label} is required`
      });
    }

    // 2. Minimum length validation
    if (minLength !== undefined) {
      rules.push({
        test: (val) => val.length >= minLength,
        message: `${label} must be at least ${minLength} character${minLength !== 1 ? 's' : ''}`
      });
    }

    // 3. Maximum length validation
    if (maxLength !== undefined) {
      rules.push({
        test: (val) => val.length <= maxLength,
        message: `${label} must be at most ${maxLength} character${maxLength !== 1 ? 's' : ''}`
      });
    }

    // 4. Minimum words validation
    if (minWords !== undefined) {
      rules.push({
        test: (val) => {
          const wordCount = val.trim().split(/\s+/).filter(word => word.length > 0).length;
          return val.trim() === '' ? !required : wordCount >= minWords;
        },
        message: `${label} must contain at least ${minWords} word${minWords !== 1 ? 's' : ''}`
      });
    }

    // 5. Maximum words validation
    if (maxWords !== undefined) {
      rules.push({
        test: (val) => {
          const wordCount = val.trim().split(/\s+/).filter(word => word.length > 0).length;
          return val.trim() === '' ? true : wordCount <= maxWords;
        },
        message: `${label} must contain at most ${maxWords} word${maxWords !== 1 ? 's' : ''}`
      });
    }

    // 6. Minimum lines validation
    if (minLines !== undefined) {
      rules.push({
        test: (val) => {
          const lineCount = val.split('\n').length;
          return val.trim() === '' ? !required : lineCount >= minLines;
        },
        message: `${label} must contain at least ${minLines} line${minLines !== 1 ? 's' : ''}`
      });
    }

    // 7. Maximum lines validation
    if (maxLines !== undefined) {
      rules.push({
        test: (val) => {
          const lineCount = val.split('\n').length;
          return lineCount <= maxLines;
        },
        message: `${label} must contain at most ${maxLines} line${maxLines !== 1 ? 's' : ''}`
      });
    }

    // 8. Pattern validation
    if (pattern) {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const regex = new RegExp(pattern);
          return regex.test(val);
        },
        message: `${label} format is invalid`
      });
    }

    // 9. Custom validation
    if (customValidation) {
      rules.push({
        test: (val) => {
          const customError = customValidation(val);
          return customError === null;
        },
        message: customValidation(value) || ''
      });
    }

    return rules;
  };

  const validateValue = (val: string): string[] => {
    const rules = getValidationRules();

    // Return only the first error found (most important)
    for (const rule of rules) {
      if (!rule.test(val)) {
        return [rule.message];
      }
    }

    return [];
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (disabled || readOnly) return;

    const newValue = e.target.value;
    onChange(newValue);

    // Real-time validation
    const newErrors = validateValue(newValue);
    setErrors(newErrors);
    onValidation?.(newErrors);
  };

  // Helper functions for counts
  const getCharacterCount = (): number => value.length;
  
  const getWordCount = (): number => {
    return value.trim().split(/\s+/).filter(word => word.length > 0).length;
  };
  
  const getLineCount = (): number => {
    return value.split('\n').length;
  };

  const hasErrors = errors.length > 0;

  // Get resize class
  const getResizeClass = (): string => {
    switch (resize) {
      case 'none':
        return 'textarea-resize-none';
      case 'horizontal':
        return 'textarea-resize-horizontal';
      case 'both':
        return 'textarea-resize-both';
      default:
        return 'textarea-resize-vertical';
    }
  };

  return (
    <div className="text-input-container">
      {/* Label */}
      <label className="text-input-label" htmlFor={id}>
        {label}
        {required && <span className="required-indicator">*</span>}
        {helpText && (
          <span
            className="text-input-info-icon"
            data-tooltip={helpText}
            aria-label={helpText}
          />
        )}
      </label>

      {/* Textarea */}
      <div className="text-input-wrapper">
        <div className={`textarea-container ${hasErrors ? 'has-error' : ''} ${disabled ? 'disabled' : ''}`}>
          <textarea
            ref={textareaRef}
            id={id}
            value={value}
            onChange={handleChange}
            disabled={disabled}
            readOnly={readOnly}
            placeholder={placeholder}
            rows={rows}
            cols={cols}
            className={`textarea-input ${getResizeClass()} ${autoResize ? 'auto-resize' : ''}`}
            aria-describedby={hasErrors ? `${id}-error` : undefined}
            aria-invalid={hasErrors}
          />

          {/* Character/Word/Line Counts */}
          {(showCharacterCount || showWordCount || showLineCount || maxLength || maxWords || maxLines) && (
            <div className="textarea-counts">
              {showCharacterCount && (
                <span className="count-item">
                  <span className="count-label">Characters:</span>
                  <span className={`count-value ${maxLength && getCharacterCount() > maxLength ? 'count-error' : ''}`}>
                    {getCharacterCount()}{maxLength && `/${maxLength}`}
                  </span>
                </span>
              )}
              
              {showWordCount && (
                <span className="count-item">
                  <span className="count-label">Words:</span>
                  <span className={`count-value ${maxWords && getWordCount() > maxWords ? 'count-error' : ''}`}>
                    {getWordCount()}{maxWords && `/${maxWords}`}
                  </span>
                </span>
              )}
              
              {showLineCount && (
                <span className="count-item">
                  <span className="count-label">Lines:</span>
                  <span className={`count-value ${maxLines && getLineCount() > maxLines ? 'count-error' : ''}`}>
                    {getLineCount()}{maxLines && `/${maxLines}`}
                  </span>
                </span>
              )}

              {/* Show limits even if counts are hidden */}
              {!showCharacterCount && maxLength && (
                <span className="count-item">
                  <span className={`count-value ${getCharacterCount() > maxLength ? 'count-error' : ''}`}>
                    {getCharacterCount()}/{maxLength}
                  </span>
                </span>
              )}
            </div>
          )}
        </div>

        {/* Error Message */}
        {hasErrors && (
          <div className="text-input-errors" role="alert" id={`${id}-error`}>
            <p className="error-message">
              {errors[0]}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ThisTextarea;
