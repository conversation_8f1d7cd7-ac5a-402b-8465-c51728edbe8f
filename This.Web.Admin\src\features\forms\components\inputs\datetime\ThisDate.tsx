// components/ThisDate.tsx
import React, { useState, useRef } from 'react';
import { Calendar, Clock } from 'lucide-react';

interface ThisDateProps {
  id: string;
  label: string;
  value: string;
  onChange: (value: string) => void;
  onValidation?: (errors: string[]) => void;
  disabled?: boolean;
  readOnly?: boolean;
  helpText?: string;
  placeholder?: string;
  required?: boolean;
  type?: 'date' | 'datetime-local' | 'time' | 'month' | 'year' | 'month-year';
  min?: string;
  max?: string;
  showIcon?: boolean;
  format?: 'ISO' | 'US' | 'EU';
  timeFormat?: '12h' | '24h';
  allowPast?: boolean;
  allowFuture?: boolean;
  allowWeekends?: boolean;
  allowedDays?: number[];
  minAge?: number;
  maxAge?: number;
  businessDaysOnly?: boolean;
  customValidation?: (value: string) => string | null;
}

interface ValidationRule {
  test: (value: string) => boolean;
  message: string;
}

const ThisDate: React.FC<ThisDateProps> = ({
  id,
  label,
  value,
  onChange,
  onValidation,
  disabled = false,
  readOnly = false,
  helpText,
  placeholder,
  required = false,
  type = 'date',
  min,
  max,
  showIcon = true,
  format = 'ISO',
  timeFormat = '24h',
  allowPast = true,
  allowFuture = true,
  allowWeekends = true,
  allowedDays,
  minAge,
  maxAge,
  businessDaysOnly = false,
  customValidation
}) => {
  const [errors, setErrors] = useState<string[]>([]);
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Helper functions
  const parseDate = (dateString: string): Date | null => {
    if (!dateString) return null;
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? null : date;
  };

  const parseTime = (timeString: string): Date | null => {
    if (!timeString) return null;
    // For time inputs, create a date with today's date and the specified time
    const today = new Date();
    const [hours, minutes] = timeString.split(':').map(Number);
    if (isNaN(hours) || isNaN(minutes)) return null;

    const date = new Date(today.getFullYear(), today.getMonth(), today.getDate(), hours, minutes);
    return isNaN(date.getTime()) ? null : date;
  };

  const parseYear = (yearString: string): Date | null => {
    if (!yearString) return null;
    const year = parseInt(yearString, 10);
    if (isNaN(year)) return null;

    const date = new Date(year, 0, 1); // January 1st of the specified year
    return isNaN(date.getTime()) ? null : date;
  };

  const parseValue = (valueString: string): Date | null => {
    if (!valueString) return null;

    switch (type) {
      case 'time':
        return parseTime(valueString);
      case 'year':
        return parseYear(valueString);
      case 'month':
      case 'month-year':
        // For month inputs, the value is in YYYY-MM format
        const date = new Date(valueString + '-01'); // Add day to make it a valid date
        return isNaN(date.getTime()) ? null : date;
      default:
        return parseDate(valueString);
    }
  };

  const formatDateForDisplay = (dateString: string): string => {
    if (!dateString) return '';
    const date = parseDate(dateString);
    if (!date) return dateString;

    switch (format) {
      case 'US':
        return date.toLocaleDateString('en-US');
      case 'EU':
        return date.toLocaleDateString('en-GB');
      default:
        return dateString;
    }
  };



  const isWeekend = (date: Date): boolean => {
    const day = date.getDay();
    return day === 0 || day === 6; // Sunday = 0, Saturday = 6
  };

  const isBusinessDay = (date: Date): boolean => {
    return !isWeekend(date);
  };

  const calculateAge = (birthDate: Date): number => {
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  // Validation rules in priority order
  const getValidationRules = (): ValidationRule[] => {
    const rules: ValidationRule[] = [];

    // 1. Required validation (highest priority)
    if (required) {
      rules.push({
        test: (val) => val.trim().length > 0,
        message: `${label} is required`
      });
    }

    // 2. Valid value validation (date/time/year based on type)
    rules.push({
      test: (val) => {
        if (val.trim() === '') return !required;
        const parsedValue = parseValue(val);
        return parsedValue !== null;
      },
      message: `${label} must be a valid ${type === 'time' ? 'time' : type === 'year' ? 'year' : type === 'month' || type === 'month-year' ? 'month' : 'date'}`
    });

    // 3. Minimum value validation
    if (min) {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const parsedValue = parseValue(val);
          const minValue = parseValue(min);
          return !parsedValue || !minValue || parsedValue >= minValue;
        },
        message: `${label} must be on or after ${type === 'time' ? min : formatDateForDisplay(min)}`
      });
    }

    // 4. Maximum value validation
    if (max) {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const parsedValue = parseValue(val);
          const maxValue = parseValue(max);
          return !parsedValue || !maxValue || parsedValue <= maxValue;
        },
        message: `${label} must be on or before ${type === 'time' ? max : formatDateForDisplay(max)}`
      });
    }

    // 5. Past date validation (skip for time-only inputs)
    if (!allowPast && type !== 'time') {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const parsedValue = parseValue(val);
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          return !parsedValue || parsedValue >= today;
        },
        message: `${label} cannot be in the past`
      });
    }

    // 6. Future date validation (skip for time-only inputs)
    if (!allowFuture && type !== 'time') {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const parsedValue = parseValue(val);
          const today = new Date();
          today.setHours(23, 59, 59, 999);
          return !parsedValue || parsedValue <= today;
        },
        message: `${label} cannot be in the future`
      });
    }

    // 7. Weekend validation (skip for time-only inputs)
    if (!allowWeekends && type !== 'time' && type !== 'year') {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const parsedValue = parseValue(val);
          return !parsedValue || !isWeekend(parsedValue);
        },
        message: `${label} cannot be on a weekend`
      });
    }

    // 8. Business days only validation (skip for time-only inputs)
    if (businessDaysOnly && type !== 'time' && type !== 'year') {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const parsedValue = parseValue(val);
          return !parsedValue || isBusinessDay(parsedValue);
        },
        message: `${label} must be a business day (Monday-Friday)`
      });
    }

    // 9. Allowed days validation (skip for time-only inputs)
    if (allowedDays && allowedDays.length > 0 && type !== 'time' && type !== 'year') {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const parsedValue = parseValue(val);
          return !parsedValue || allowedDays.includes(parsedValue.getDay());
        },
        message: `${label} must be on an allowed day of the week`
      });
    }

    // 10. Minimum age validation (only for date inputs)
    if (minAge !== undefined && type === 'date') {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const parsedValue = parseValue(val);
          return !parsedValue || calculateAge(parsedValue) >= minAge;
        },
        message: `${label} must indicate an age of at least ${minAge} years`
      });
    }

    // 11. Maximum age validation (only for date inputs)
    if (maxAge !== undefined && type === 'date') {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const parsedValue = parseValue(val);
          return !parsedValue || calculateAge(parsedValue) <= maxAge;
        },
        message: `${label} must indicate an age of at most ${maxAge} years`
      });
    }

    // 12. Custom validation
    if (customValidation) {
      rules.push({
        test: (val) => {
          const customError = customValidation(val);
          return customError === null;
        },
        message: customValidation(value) || ''
      });
    }

    return rules;
  };

  const validateValue = (val: string): string[] => {
    const rules = getValidationRules();

    // Return only the first error found (most important)
    for (const rule of rules) {
      if (!rule.test(val)) {
        return [rule.message];
      }
    }

    return [];
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled || readOnly) return;

    const newValue = e.target.value;
    onChange(newValue);

    // Real-time validation
    const newErrors = validateValue(newValue);
    setErrors(newErrors);
    onValidation?.(newErrors);
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const getIcon = () => {
    if (type === 'time') {
      return <Clock size={16} />;
    }
    return <Calendar size={16} />;
  };

  const getInputType = (): string => {
    switch (type) {
      case 'year':
        return 'number';
      case 'month-year':
        return 'month';
      default:
        return type;
    }
  };

  const getPlaceholderText = (): string => {
    if (placeholder) return placeholder;

    switch (type) {
      case 'date':
        return 'Select date...';
      case 'datetime-local':
        return timeFormat === '12h' ? 'Select date and time (12h)...' : 'Select date and time (24h)...';
      case 'time':
        return timeFormat === '12h' ? 'Select time (12h)...' : 'Select time (24h)...';
      case 'month':
        return 'Select month...';
      case 'year':
        return 'Enter year...';
      case 'month-year':
        return 'Select month and year...';
      default:
        return 'Select...';
    }
  };

  const hasErrors = errors.length > 0;

  return (
    <div className="text-input-container">
      {/* Label */}
      <label className="text-input-label" htmlFor={id}>
        {label}
        {required && <span className="required-indicator">*</span>}
        {helpText && (
          <span
            className="text-input-info-icon"
            data-tooltip={helpText}
            aria-label={helpText}
          />
        )}
      </label>

      {/* Input */}
      <div className="text-input-wrapper">
        <div className={`date-input-container ${hasErrors ? 'has-error' : ''} ${disabled ? 'disabled' : ''} ${isFocused ? 'focused' : ''}`}>
          <input
            ref={inputRef}
            id={id}
            type={getInputType()}
            value={value}
            onChange={handleChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            disabled={disabled}
            readOnly={readOnly}
            placeholder={getPlaceholderText()}
            className="date-input"
            aria-describedby={hasErrors ? `${id}-error` : undefined}
            aria-invalid={hasErrors}
            min={type === 'year' ? (min ? new Date(min).getFullYear().toString() : '1900') : min}
            max={type === 'year' ? (max ? new Date(max).getFullYear().toString() : '2100') : max}
            step={type === 'year' ? '1' : undefined}
          />

          {showIcon && (
            <div className="date-icon">
              {getIcon()}
            </div>
          )}
        </div>

        {/* Helper Text */}
        {!hasErrors && (
          <div className="date-helper">
            {(type === 'time' || type === 'datetime-local') && (
              <span className="helper-text">
                Time format: {timeFormat === '12h' ? '12-hour (AM/PM)' : '24-hour'}
              </span>
            )}
            {!allowPast && !allowFuture && (
              <span className="helper-text">Today only</span>
            )}
            {!allowPast && allowFuture && (
              <span className="helper-text">Today or future dates only</span>
            )}
            {allowPast && !allowFuture && (
              <span className="helper-text">Today or past dates only</span>
            )}
            {businessDaysOnly && (
              <span className="helper-text">Business days only (Monday-Friday)</span>
            )}
            {!allowWeekends && !businessDaysOnly && (
              <span className="helper-text">Weekdays only</span>
            )}
            {minAge !== undefined && maxAge !== undefined && (
              <span className="helper-text">Age must be between {minAge} and {maxAge} years</span>
            )}
            {minAge !== undefined && maxAge === undefined && (
              <span className="helper-text">Must be at least {minAge} years old</span>
            )}
            {minAge === undefined && maxAge !== undefined && (
              <span className="helper-text">Must be at most {maxAge} years old</span>
            )}
          </div>
        )}

        {/* Error Message */}
        {hasErrors && (
          <div className="text-input-errors" role="alert" id={`${id}-error`}>
            <p className="error-message">
              {errors[0]}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ThisDate;
