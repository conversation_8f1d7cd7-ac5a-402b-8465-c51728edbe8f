import React, { useState, useEffect } from 'react';
import { Form } from 'react-bootstrap';
import type { MetadataItem } from '../../utils/index';
import { contextService } from '../../services/contextService';
import type { Context } from '../../types/context';

interface RenderEditCellProps {
  row: any;
  column: any;
  onRowChange: (row: any) => void;
  onClose: (commitChanges?: boolean) => void;
  updateTempMetadataRow: (rowId: string, updates: Partial<MetadataItem>) => void;
}

export const CustomContextEditor: React.FC<RenderEditCellProps> = ({
  row,
  onClose,
  updateTempMetadataRow
}) => {
  const [contexts, setContexts] = useState<Context[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadContexts = async () => {
      try {
        setLoading(true);
        setError(null);
        const contextData = await contextService.getAllContexts(false); // Only active contexts
        setContexts(contextData);
      } catch (err) {
        console.error('Failed to load contexts:', err);
        setError('Failed to load contexts');
        setContexts([]);
      } finally {
        setLoading(false);
      }
    };

    loadContexts();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedContextId = e.target.value;
    const selectedContext = contexts.find(c => c.id === selectedContextId);
    
    const updates: Partial<MetadataItem> = {
      contextId: selectedContextId || undefined,
      contextName: selectedContext?.name || undefined
    };

    updateTempMetadataRow(row._internalId, updates);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === 'Tab') {
      e.preventDefault();
      onClose(true);
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onClose(false);
    }
  };

  if (loading) {
    return (
      <div className="p-2 text-center">
        <small className="text-muted">Loading contexts...</small>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-2 text-center">
        <small className="text-danger">{error}</small>
      </div>
    );
  }

  return (
    <Form.Select
      value={row.contextId || ''}
      onChange={handleChange}
      onKeyDown={handleKeyDown}
      onBlur={() => onClose(true)}
      autoFocus
      size="sm"
      style={{ 
        border: 'none', 
        outline: 'none', 
        boxShadow: 'none',
        background: 'transparent'
      }}
    >
      <option value="">Select Context (Optional)</option>
      {contexts.map((context) => (
        <option key={context.id} value={context.id}>
          {context.name}
          {context.category && ` (${context.category})`}
        </option>
      ))}
    </Form.Select>
  );
};
