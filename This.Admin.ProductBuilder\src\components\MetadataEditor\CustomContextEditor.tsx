import React, { useState, useEffect } from 'react';
import { Form } from 'react-bootstrap';
import type { MetadataItem } from '../../utils/index';
import { contextService } from '../../services/contextService';
import type { Context } from '../../types/context';

interface RenderEditCellProps {
  row: any;
  column: any;
  onRowChange: (row: any) => void;
  onClose: (commitChanges?: boolean) => void;
  updateTempMetadataRow: (rowId: string, updates: Partial<MetadataItem>) => void;
  contexts?: Context[]; // Pre-loaded contexts
  contextsLoading?: boolean;
  contextsError?: string | null;
}

// Global context cache to avoid repeated API calls
let globalContexts: Context[] | null = null;
let globalContextsPromise: Promise<Context[]> | null = null;

const loadContextsOnce = async (): Promise<Context[]> => {
  if (globalContexts) {
    return globalContexts;
  }

  if (globalContextsPromise) {
    return globalContextsPromise;
  }

  globalContextsPromise = contextService.getAllContexts(false)
    .then(contexts => {
      globalContexts = contexts;
      globalContextsPromise = null;
      return contexts;
    })
    .catch(err => {
      globalContextsPromise = null;
      throw err;
    });

  return globalContextsPromise;
};

export const CustomContextEditor: React.FC<RenderEditCellProps> = ({
  row,
  onClose,
  updateTempMetadataRow,
  contexts: preloadedContexts,
  contextsLoading: preloadedLoading,
  contextsError: preloadedError
}) => {
  const [contexts, setContexts] = useState<Context[]>(preloadedContexts || globalContexts || []);
  const [loading, setLoading] = useState(preloadedLoading || false);
  const [error, setError] = useState<string | null>(preloadedError || null);

  useEffect(() => {
    // If contexts are already provided, don't load them again
    if (preloadedContexts) {
      setContexts(preloadedContexts);
      setLoading(false);
      setError(preloadedError || null);
      return;
    }

    // If we already have global contexts, use them
    if (globalContexts) {
      setContexts(globalContexts);
      setLoading(false);
      setError(null);
      return;
    }

    // Load contexts only once
    const loadContexts = async () => {
      try {
        setLoading(true);
        setError(null);
        const contextData = await loadContextsOnce();
        setContexts(contextData);
      } catch (err) {
        console.error('Failed to load contexts:', err);
        setError('Failed to load contexts');
        setContexts([]);
      } finally {
        setLoading(false);
      }
    };

    loadContexts();
  }, [preloadedContexts, preloadedLoading, preloadedError]);

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedContextId = e.target.value;
    const selectedContext = contexts.find(c => c.id === selectedContextId);
    
    const updates: Partial<MetadataItem> = {
      contextId: selectedContextId || undefined,
      contextName: selectedContext?.name || undefined
    };

    updateTempMetadataRow(row._internalId, updates);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === 'Tab') {
      e.preventDefault();
      onClose(true);
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onClose(false);
    }
  };

  if (loading) {
    return (
      <div className="p-2 text-center">
        <small className="text-muted">Loading contexts...</small>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-2 text-center">
        <small className="text-danger">{error}</small>
      </div>
    );
  }

  return (
    <Form.Select
      value={row.contextId || ''}
      onChange={handleChange}
      onKeyDown={handleKeyDown}
      onBlur={() => onClose(true)}
      autoFocus
      size="sm"
      style={{ 
        border: 'none', 
        outline: 'none', 
        boxShadow: 'none',
        background: 'transparent'
      }}
    >
      <option value="">Select Context (Optional)</option>
      {contexts.map((context) => (
        <option key={context.id} value={context.id}>
          {context.name}
          {context.category && ` (${context.category})`}
        </option>
      ))}
    </Form.Select>
  );
};
