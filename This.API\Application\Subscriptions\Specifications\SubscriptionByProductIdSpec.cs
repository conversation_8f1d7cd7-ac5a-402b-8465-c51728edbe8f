using Ardalis.Specification;
using Domain.Entities;

namespace Application.Subscriptions.Specifications;

/// <summary>
/// Specification to get subscriptions by product ID
/// </summary>
public class SubscriptionByProductIdSpec : Specification<Subscription>
{
    public SubscriptionByProductIdSpec(Guid productId)
    {
        Query.Where(s => s.ProductId == productId && !s.IsDeleted)
             .Include(s => s.Product);
    }
}
