using Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for Template entity
/// </summary>
public class TemplateConfig : IEntityTypeConfiguration<Template>
{
    public void Configure(EntityTypeBuilder<Template> builder)
    {
        builder.ToTable("Templates", "Genp");

        // Primary key
        builder.HasKey(e => e.Id);

        // Properties
        builder.Property(e => e.Name)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(e => e.Version)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.Stage)
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(e => e.TemplateJson)
            .HasDefaultValue("{}")
            .IsRequired();

        builder.Property(e => e.PublishedAt);
       
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false);

        // Indexes
        builder.HasIndex(e => e.IsDeleted)
            .HasDatabaseName("IX_Templates_IsDeleted");

        // Note: No relationship with Product table - ProductId is just a reference field
    }
}
