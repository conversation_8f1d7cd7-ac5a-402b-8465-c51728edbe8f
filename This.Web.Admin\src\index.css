@import "./styles/themes.css";
@import "./styles/animations.css";
@import "./styles/navigation.css";

/* AG-Grid CSS imports */
@import "ag-grid-community/styles/ag-grid.css";
@import "ag-grid-community/styles/ag-theme-alpine.css";
@import "./styles/ag-grid-theme.css";

body {
  margin: 0;
  background-color: rgb(var(--color-background));
  color: rgb(var(--color-foreground));
  font-feature-settings: "rlig" 1, "calt" 1;
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Ensure all elements use the custom font family */
*,
*::before,
*::after {
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
}

/* Specifically target buttons and form elements */
button,
input,
textarea,
select,
.btn,
[role="button"] {
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif !important;
}

/* TextInput Component Styles */
.text-input-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.text-input-label {
  display: block;
  font-size: 0.75rem;
  font-weight: 500;
  color: rgb(var(--color-surface-700));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  position: relative;
  overflow: visible;
}

[data-theme="dark"] .text-input-label {
  color: rgb(var(--color-surface-300));
}

.required-indicator {
  color: #ef4444;
  margin-left: 0.25rem;
}

.text-input-wrapper {
  position: relative;
  margin-bottom: 1.5rem; /* Space for error messages */
  overflow: visible;
}

.text-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid rgb(var(--color-surface-300));
  border-radius: 0.5rem;
  background-color: rgb(var(--color-surface-50));
  color: rgb(var(--color-surface-900));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  transition: all 0.2s ease-in-out;
}

/* Add right padding when character count is present */
.text-input.has-character-count {
  padding-right: 3.5rem;
}

.text-input::placeholder {
  color: rgb(var(--color-surface-400));
}

.text-input:focus {
  outline: none;
  border-color: rgb(var(--color-primary-500));
  box-shadow: 0 0 0 2px rgba(var(--color-primary-500), 0.2);
}

[data-theme="dark"] .text-input {
  border-color: rgb(var(--color-surface-600));
  background-color: rgb(var(--color-surface-800));
  color: rgb(var(--color-surface-100));
}

[data-theme="dark"] .text-input::placeholder {
  color: rgb(var(--color-surface-400));
}

.text-input.has-error {
  border-color: #fca5a5;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
}

.text-input.has-error:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.text-input.disabled {
  background-color: rgb(var(--color-surface-100));
  color: rgb(var(--color-surface-500));
  cursor: not-allowed;
}

[data-theme="dark"] .text-input.disabled {
  background-color: rgb(var(--color-surface-700));
  color: rgb(var(--color-surface-400));
}

.text-input-display {
  padding: 0.5rem 0.75rem;
  border: 1px solid transparent;
  border-radius: 0.5rem;
  background-color: rgb(var(--color-surface-50));
  color: rgb(var(--color-surface-900));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
}

[data-theme="dark"] .text-input-display {
  background-color: rgb(var(--color-surface-800));
  color: rgb(var(--color-surface-100));
}

.empty-value {
  color: rgb(var(--color-surface-400));
  font-style: italic;
}

.validation-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
}

/* Info icon next to label */
.text-input-info-icon {
  position: relative;
  display: inline-block;
  margin-left: 0.375rem;
  cursor: help;
  vertical-align: middle;
}

.text-input-info-icon::before {
  content: "ℹ";
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;
  background-color: rgb(var(--color-secondary-500));
  color: white;
  border-radius: 50%;
  font-size: 0.625rem;
  font-weight: 600;
  line-height: 1;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.text-input-info-icon:hover::before {
  background-color: rgb(var(--color-secondary-600));
  transform: scale(1.1);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* Override the arrow styling when not hovering */
.text-input-info-icon:not(:hover)::before {
  content: "ℹ";
  border: none;
  background-color: rgb(var(--color-secondary-500));
  color: white;
  border-radius: 50%;
  bottom: auto;
  left: auto;
  transform: none;
  animation: none;
}

/* Simple tooltip without arrow for now */
.text-input-info-icon:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: calc(100% + 8px);
  left: 50%;
  transform: translateX(-50%);
  background: rgb(var(--color-surface-900));
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.625rem;
  font-weight: 400;
  line-height: 1.4;
  white-space: nowrap;
  max-width: 200px;
  white-space: normal;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: tooltipFadeIn 0.2s ease-out;
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  pointer-events: none;
}

/* Dark theme tooltip */
[data-theme="dark"] .text-input-info-icon:hover::after {
  background: rgb(var(--color-surface-100));
  color: rgb(var(--color-surface-900));
}

/* Tooltip animation */
@keyframes tooltipFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.text-input-errors {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-top: 0.25rem;
}

.error-message {
  font-size: 0.75rem;
  color: #dc2626;
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  background-color: rgb(var(--color-background));
  padding: 0.125rem 0;
  line-height: 1.2;
}

[data-theme="dark"] .error-message {
  color: #f87171;
}

.character-count {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.625rem;
  color: rgb(var(--color-surface-500));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  pointer-events: none;
  z-index: 5;
  background-color: rgb(var(--color-surface-50));
  padding: 0 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.character-count.warning {
  color: rgb(var(--color-warning-600));
  background-color: rgb(var(--color-warning-50));
  border: 1px solid rgb(var(--color-warning-300));
}

.character-count.error {
  color: rgb(var(--color-error-600));
  background-color: rgb(var(--color-error-50));
  border: 1px solid rgb(var(--color-error-300));
}

[data-theme="dark"] .character-count {
  background-color: rgb(var(--color-surface-800));
  color: rgb(var(--color-surface-400));
}

[data-theme="dark"] .character-count.warning {
  color: rgb(var(--color-warning-400));
  background-color: rgb(var(--color-warning-900));
  border-color: rgb(var(--color-warning-700));
}

[data-theme="dark"] .character-count.error {
  color: rgb(var(--color-error-400));
  background-color: rgb(var(--color-error-900));
  border-color: rgb(var(--color-error-700));
}

/* Clear button styling */
.text-input-clear-button {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.5rem;
  height: 1.5rem;
  border: none;
  background-color: rgb(var(--color-surface-300));
  color: rgb(var(--color-surface-600));
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  line-height: 1;
  transition: all 0.2s ease;
  z-index: 6;
  opacity: 0.7;
}

.text-input-clear-button:hover {
  background-color: rgb(var(--color-surface-400));
  color: rgb(var(--color-surface-700));
  opacity: 1;
  transform: translateY(-50%) scale(1.1);
}

.text-input-clear-button:active {
  transform: translateY(-50%) scale(0.95);
}

[data-theme="dark"] .text-input-clear-button {
  background-color: rgb(var(--color-surface-600));
  color: rgb(var(--color-surface-300));
}

[data-theme="dark"] .text-input-clear-button:hover {
  background-color: rgb(var(--color-surface-500));
  color: rgb(var(--color-surface-200));
}

/* Adjust input padding when clear button is present */
.text-input-wrapper:has(.text-input-clear-button) .text-input {
  padding-right: 2.5rem;
}

/* Adjust character count position when clear button is present */
.text-input-wrapper:has(.text-input-clear-button) .character-count {
  right: 3rem;
}

.view-mode .text-input-label {
  color: rgb(var(--color-surface-600));
}

[data-theme="dark"] .view-mode .text-input-label {
  color: rgb(var(--color-surface-400));
}

/* Color Preview Styles */
.color-preview-primary {
  background-color: rgb(var(--color-primary-500));
  color: rgb(var(--color-primary-50));
}

.color-preview-secondary {
  background-color: rgb(var(--color-secondary-500));
  color: rgb(var(--color-secondary-50));
}

.color-preview-accent {
  background-color: rgb(var(--color-accent-500));
  color: rgb(var(--color-accent-50));
}

/* Dark theme adjustments for better contrast */
[data-theme="dark"] .color-preview-primary {
  background-color: rgb(var(--color-primary-500));
  color: rgb(var(--color-primary-900));
}

[data-theme="dark"] .color-preview-secondary {
  background-color: rgb(var(--color-secondary-500));
  color: rgb(var(--color-secondary-900));
}

[data-theme="dark"] .color-preview-accent {
  background-color: rgb(var(--color-accent-500));
  color: rgb(var(--color-accent-900));
}

/* Tailwind Utility Classes for Theme System */
.bg-background {
  background-color: rgb(var(--color-background));
}

.text-foreground {
  color: rgb(var(--color-foreground));
}

.bg-card {
  background-color: rgb(var(--color-card));
}

.text-card-foreground {
  color: rgb(var(--color-card-foreground));
}

.bg-muted {
  background-color: rgb(var(--color-muted));
}

.text-muted-foreground {
  color: rgb(var(--color-muted-foreground));
}

.border-border {
  border-color: rgb(var(--color-border));
}

.bg-primary {
  background-color: rgb(var(--color-primary-500));
}

.text-primary {
  color: rgb(var(--color-primary-500));
}

.text-primary-foreground {
  color: rgb(var(--color-primary-50));
}

.bg-secondary {
  background-color: rgb(var(--color-secondary-500));
}

.text-secondary {
  color: rgb(var(--color-secondary-500));
}

.text-secondary-foreground {
  color: rgb(var(--color-secondary-50));
}

.bg-accent {
  background-color: rgb(var(--color-accent-500));
}

.text-accent {
  color: rgb(var(--color-accent-500));
}

.text-accent-foreground {
  color: rgb(var(--color-accent-50));
}

/* Dark theme adjustments for utility classes */
[data-theme="dark"] .text-primary-foreground {
  color: rgb(var(--color-primary-900));
}

[data-theme="dark"] .text-secondary-foreground {
  color: rgb(var(--color-secondary-900));
}

[data-theme="dark"] .text-accent-foreground {
  color: rgb(var(--color-accent-900));
}

/* Enhanced Theme Transitions */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Shadow utilities that work with themes */
.shadow-theme {
  box-shadow: 0 1px 3px 0 rgba(var(--color-surface-900), 0.1), 0 1px 2px 0 rgba(var(--color-surface-900), 0.06);
}

.shadow-theme-lg {
  box-shadow: 0 10px 15px -3px rgba(var(--color-surface-900), 0.1), 0 4px 6px -2px rgba(var(--color-surface-900), 0.05);
}

[data-theme="dark"] .shadow-theme {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .shadow-theme-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

/* Ring utilities for focus states */
.ring-theme {
  --tw-ring-color: rgb(var(--color-primary-500));
}

/* Improved focus styles */
.focus-theme:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--color-primary-500), 0.2);
}

/* Breadcrumb Button Focus Styles - Override any conflicting styles */
.breadcrumb-button {
  outline: none !important;
}

.breadcrumb-button:focus {
  outline: none !important;
  box-shadow: none !important;
}

.breadcrumb-button:focus-visible {
  outline: none !important;
  box-shadow: 0 0 0 2px rgb(var(--color-primary-500)) !important;
  border-radius: 0.125rem;
}

/* Ensure no browser default outlines on breadcrumb buttons */
.breadcrumb-button::-moz-focus-inner {
  border: 0;
  outline: none;
}

.breadcrumb-button:focus:not(:focus-visible) {
  outline: none !important;
  box-shadow: none !important;
}

/* Additional browser-specific outline removal */
.breadcrumb-button:focus,
.breadcrumb-button:active {
  outline: 0 !important;
  outline-width: 0 !important;
  outline-style: none !important;
  outline-color: transparent !important;
}

/* Webkit-specific outline removal */
.breadcrumb-button:focus {
  -webkit-appearance: none;
  -webkit-focus-ring-color: transparent !important;
}

/* Dark theme support for breadcrumb focus */
[data-theme="dark"] .breadcrumb-button:focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--color-primary-400)) !important;
}

/* Currency Input Component Styles */
.currency-input-container {
  display: flex;
  width: 100%;
  border: 1px solid rgb(var(--color-surface-300));
  border-radius: 0.5rem;
  background-color: rgb(var(--color-surface-50));
  transition: all 0.2s ease-in-out;
  overflow: visible;
  position: relative;
}

.currency-input-container:focus-within {
  border-color: rgb(var(--color-primary-500));
  box-shadow: 0 0 0 2px rgba(var(--color-primary-500), 0.2);
}

.currency-input-container.has-error {
  border-color: #fca5a5;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
}

.currency-input-container.has-error:focus-within {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.currency-input-container.disabled {
  background-color: rgb(var(--color-surface-100));
  cursor: not-allowed;
}

[data-theme="dark"] .currency-input-container {
  border-color: rgb(var(--color-surface-600));
  background-color: rgb(var(--color-surface-800));
}

[data-theme="dark"] .currency-input-container.disabled {
  background-color: rgb(var(--color-surface-700));
}

/* Currency Dropdown */
.currency-dropdown {
  position: relative;
  flex-shrink: 0;
}

.currency-dropdown-trigger {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: none;
  border: none;
  border-right: 1px solid rgb(var(--color-surface-300));
  color: rgb(var(--color-surface-700));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 4.5rem;
  min-width: 4.5rem;
  max-width: 4.5rem;
  flex-shrink: 0;
}

.currency-dropdown-trigger:hover:not(:disabled) {
  background-color: rgb(var(--color-surface-100));
}

.currency-dropdown-trigger:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

[data-theme="dark"] .currency-dropdown-trigger {
  border-right-color: rgb(var(--color-surface-600));
  color: rgb(var(--color-surface-300));
}

[data-theme="dark"] .currency-dropdown-trigger:hover:not(:disabled) {
  background-color: rgb(var(--color-surface-700));
}

.currency-code {
  font-size: 0.875rem;
  font-weight: 600;
}

.currency-dropdown-icon {
  transition: transform 0.2s ease;
  color: rgb(var(--color-surface-500));
}

.currency-dropdown-icon.open {
  transform: rotate(180deg);
}

/* Currency Dropdown Menu */
.currency-dropdown-menu {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  z-index: 1000;
  background-color: rgb(var(--color-surface-50));
  border: 1px solid rgb(var(--color-surface-300));
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(var(--color-surface-900), 0.1), 0 4px 6px -2px rgba(var(--color-surface-900), 0.05);
  max-height: 16rem;
  overflow: hidden;
  width: 320px;
  min-width: 320px;
}

[data-theme="dark"] .currency-dropdown-menu {
  background-color: rgb(var(--color-surface-800));
  border-color: rgb(var(--color-surface-600));
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

/* Currency Search */
.currency-search {
  padding: 0.75rem;
  border-bottom: 1px solid rgb(var(--color-surface-200));
}

[data-theme="dark"] .currency-search {
  border-bottom-color: rgb(var(--color-surface-600));
}

.currency-search-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid rgb(var(--color-surface-300));
  border-radius: 0.375rem;
  background-color: rgb(var(--color-surface-50));
  color: rgb(var(--color-surface-900));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  font-size: 0.875rem;
}

.currency-search-input:focus {
  outline: none;
  border-color: rgb(var(--color-primary-500));
  box-shadow: 0 0 0 2px rgba(var(--color-primary-500), 0.2);
}

[data-theme="dark"] .currency-search-input {
  border-color: rgb(var(--color-surface-600));
  background-color: rgb(var(--color-surface-700));
  color: rgb(var(--color-surface-100));
}

/* Currency List */
.currency-list {
  max-height: 12rem;
  overflow-y: auto;
}

.currency-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
}

.currency-option:hover {
  background-color: rgb(var(--color-surface-100));
}

.currency-option.selected {
  background-color: rgb(var(--color-primary-50));
  color: rgb(var(--color-primary-700));
}

[data-theme="dark"] .currency-option:hover {
  background-color: rgb(var(--color-surface-700));
}

[data-theme="dark"] .currency-option.selected {
  background-color: rgb(var(--color-primary-900));
  color: rgb(var(--color-primary-300));
}

.currency-option-code {
  font-weight: 600;
  font-size: 0.875rem;
  min-width: 3rem;
  color: rgb(var(--color-surface-900));
}

.currency-option-symbol {
  font-weight: 500;
  font-size: 0.875rem;
  min-width: 2rem;
  color: rgb(var(--color-surface-700));
}

.currency-option-name {
  font-size: 0.875rem;
  color: rgb(var(--color-surface-600));
  flex: 1;
}

[data-theme="dark"] .currency-option-code {
  color: rgb(var(--color-surface-100));
}

[data-theme="dark"] .currency-option-symbol {
  color: rgb(var(--color-surface-300));
}

[data-theme="dark"] .currency-option-name {
  color: rgb(var(--color-surface-400));
}

/* Amount Input */
.currency-amount-input {
  flex: 1;
  padding: 0.5rem 0.75rem;
  border: none;
  background: none;
  color: rgb(var(--color-surface-900));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  font-size: 1rem;
  outline: none;
}

.currency-amount-input::placeholder {
  color: rgb(var(--color-surface-400));
}

.currency-amount-input:disabled {
  color: rgb(var(--color-surface-500));
  cursor: not-allowed;
}

[data-theme="dark"] .currency-amount-input {
  color: rgb(var(--color-surface-100));
}

[data-theme="dark"] .currency-amount-input::placeholder {
  color: rgb(var(--color-surface-400));
}

[data-theme="dark"] .currency-amount-input:disabled {
  color: rgb(var(--color-surface-400));
}

/* Dropdown Backdrop */
.currency-dropdown-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: transparent;
}

/* Checkbox Input Component Styles */
.checkbox-input-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 0.75rem;
  border: 1px solid rgb(var(--color-surface-300));
  border-radius: 0.5rem;
  background-color: rgb(var(--color-surface-50));
  transition: all 0.2s ease-in-out;
}

.checkbox-input-container:focus-within {
  border-color: rgb(var(--color-primary-500));
  box-shadow: 0 0 0 2px rgba(var(--color-primary-500), 0.2);
}

.checkbox-input-container.has-error {
  border-color: #fca5a5;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
}

.checkbox-input-container.has-error:focus-within {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.checkbox-input-container.disabled {
  background-color: rgb(var(--color-surface-100));
  cursor: not-allowed;
}

[data-theme="dark"] .checkbox-input-container {
  border-color: rgb(var(--color-surface-600));
  background-color: rgb(var(--color-surface-800));
}

[data-theme="dark"] .checkbox-input-container.disabled {
  background-color: rgb(var(--color-surface-700));
}

/* Checkbox Options Layout */
.checkbox-options-vertical {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.checkbox-options-horizontal {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.checkbox-options-grid {
  display: grid;
  gap: 0.5rem;
}

.checkbox-grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.checkbox-grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.checkbox-grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* Individual Checkbox Option */
.checkbox-option {
  display: flex;
  align-items: flex-start;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  cursor: pointer;
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  font-size: 0.75rem;
  line-height: 1.4;
  user-select: none;
}

.checkbox-label:hover .checkbox-custom:not(.disabled):not(.checked):not(.indeterminate) {
  border-color: rgb(var(--color-primary-400));
  background-color: rgb(var(--color-primary-50));
}

.checkbox-label:hover .checkbox-custom.checked:not(.disabled) {
  border-color: rgb(var(--color-primary-600));
  background-color: rgb(var(--color-primary-600));
}

.checkbox-label:hover .checkbox-custom.indeterminate:not(.disabled) {
  border-color: rgb(var(--color-primary-600));
  background-color: rgb(var(--color-primary-600));
}

.checkbox-label:hover .checkbox-text:not(.disabled) {
  color: rgb(var(--color-surface-900));
}

[data-theme="dark"] .checkbox-label:hover .checkbox-custom:not(.disabled):not(.checked):not(.indeterminate) {
  border-color: rgb(var(--color-primary-400));
  background-color: rgb(var(--color-primary-900));
}

[data-theme="dark"] .checkbox-label:hover .checkbox-custom.checked:not(.disabled) {
  border-color: rgb(var(--color-primary-300));
  background-color: rgb(var(--color-primary-400));
}

[data-theme="dark"] .checkbox-label:hover .checkbox-custom.indeterminate:not(.disabled) {
  border-color: rgb(var(--color-primary-300));
  background-color: rgb(var(--color-primary-400));
}

[data-theme="dark"] .checkbox-label:hover .checkbox-text:not(.disabled) {
  color: rgb(var(--color-surface-100));
}

/* Checkbox Wrapper */
.checkbox-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 0.125rem; /* Align with first line of text */
}

/* Hidden Native Checkbox */
.checkbox-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
  pointer-events: none;
}

/* Custom Checkbox */
.checkbox-custom {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.125rem;
  height: 1.125rem;
  border: 2px solid rgb(var(--color-surface-400));
  border-radius: 0.25rem;
  background-color: rgb(var(--color-surface-50));
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.checkbox-custom.checked {
  border-color: rgb(var(--color-primary-500));
  background-color: rgb(var(--color-primary-500));
}

.checkbox-custom.indeterminate {
  border-color: rgb(var(--color-primary-500));
  background-color: rgb(var(--color-primary-500));
}

.checkbox-custom.disabled {
  border-color: rgb(var(--color-surface-300));
  background-color: rgb(var(--color-surface-100));
  cursor: not-allowed;
}

[data-theme="dark"] .checkbox-custom {
  border-color: rgb(var(--color-surface-500));
  background-color: rgb(var(--color-surface-800));
}

[data-theme="dark"] .checkbox-custom.checked {
  border-color: rgb(var(--color-primary-400));
  background-color: rgb(var(--color-primary-500));
}

[data-theme="dark"] .checkbox-custom.indeterminate {
  border-color: rgb(var(--color-primary-400));
  background-color: rgb(var(--color-primary-500));
}

[data-theme="dark"] .checkbox-custom.disabled {
  border-color: rgb(var(--color-surface-600));
  background-color: rgb(var(--color-surface-700));
}

/* Checkbox Icon */
.checkbox-icon {
  color: white;
  stroke-width: 3;
}

/* Indeterminate Icon */
.checkbox-indeterminate-icon {
  width: 0.5rem;
  height: 0.125rem;
  background-color: white;
  border-radius: 0.0625rem;
}

/* Checkbox Text */
.checkbox-text {
  color: rgb(var(--color-surface-900));
  font-weight: 400;
}

.checkbox-text.disabled {
  color: rgb(var(--color-surface-500));
  cursor: not-allowed;
}

[data-theme="dark"] .checkbox-text {
  color: rgb(var(--color-surface-100));
}

[data-theme="dark"] .checkbox-text.disabled {
  color: rgb(var(--color-surface-400));
}

/* Select All Option */
.select-all-option {
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgb(var(--color-surface-200));
  margin-bottom: 0.25rem;
}

[data-theme="dark"] .select-all-option {
  border-bottom-color: rgb(var(--color-surface-600));
}

.select-all-text {
  font-weight: 400;
  color: rgb(var(--color-surface-700));
}

[data-theme="dark"] .select-all-text {
  color: rgb(var(--color-surface-300));
}

/* Selection Count */
.checkbox-selection-count {
  padding-top: 0.5rem;
  border-top: 1px solid rgb(var(--color-surface-200));
  margin-top: 0.25rem;
}

[data-theme="dark"] .checkbox-selection-count {
  border-top-color: rgb(var(--color-surface-600));
}

.selection-count-text {
  font-size: 0.625rem;
  color: rgb(var(--color-surface-600));
  font-weight: 400;
}

[data-theme="dark"] .selection-count-text {
  color: rgb(var(--color-surface-400));
}

/* Radio Input Component Styles */
.radio-input-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 0.75rem;
  border: 1px solid rgb(var(--color-surface-300));
  border-radius: 0.5rem;
  background-color: rgb(var(--color-surface-50));
  transition: all 0.2s ease-in-out;
}

.radio-input-container:focus-within {
  border-color: rgb(var(--color-primary-500));
  box-shadow: 0 0 0 2px rgba(var(--color-primary-500), 0.2);
}

.radio-input-container.has-error {
  border-color: #fca5a5;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
}

.radio-input-container.has-error:focus-within {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.radio-input-container.disabled {
  background-color: rgb(var(--color-surface-100));
  cursor: not-allowed;
}

[data-theme="dark"] .radio-input-container {
  border-color: rgb(var(--color-surface-600));
  background-color: rgb(var(--color-surface-800));
}

[data-theme="dark"] .radio-input-container.disabled {
  background-color: rgb(var(--color-surface-700));
}

/* Radio Options Layout */
.radio-options-vertical {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.radio-options-horizontal {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
}

.radio-options-grid {
  display: grid;
  gap: 0.75rem;
}

.radio-grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.radio-grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.radio-grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* Individual Radio Option */
.radio-option {
  display: flex;
  align-items: flex-start;
}

.radio-label {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  font-size: 0.75rem;
  line-height: 1.4;
  user-select: none;
  width: 100%;
}

.radio-label:hover .radio-custom:not(.disabled):not(.checked) {
  border-color: rgb(var(--color-primary-400));
  background-color: rgb(var(--color-primary-50));
}

.radio-label:hover .radio-custom.checked:not(.disabled) {
  border-color: rgb(var(--color-primary-600));
  background-color: rgb(var(--color-surface-50));
}

.radio-label:hover .radio-text:not(.disabled) {
  color: rgb(var(--color-surface-900));
}

.radio-label:hover .radio-description {
  color: rgb(var(--color-surface-600));
}

[data-theme="dark"] .radio-label:hover .radio-custom:not(.disabled):not(.checked) {
  border-color: rgb(var(--color-primary-400));
  background-color: rgb(var(--color-primary-900));
}

[data-theme="dark"] .radio-label:hover .radio-custom.checked:not(.disabled) {
  border-color: rgb(var(--color-primary-300));
  background-color: rgb(var(--color-surface-800));
}

[data-theme="dark"] .radio-label:hover .radio-text:not(.disabled) {
  color: rgb(var(--color-surface-100));
}

[data-theme="dark"] .radio-label:hover .radio-description {
  color: rgb(var(--color-surface-400));
}

/* Radio Wrapper */
.radio-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 0.125rem; /* Align with first line of text */
}

/* Hidden Native Radio */
.radio-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
  pointer-events: none;
}

/* Custom Radio */
.radio-custom {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid rgb(var(--color-surface-400));
  border-radius: 50%;
  background-color: rgb(var(--color-surface-50));
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.radio-custom.checked {
  border-color: rgb(var(--color-primary-500));
  background-color: rgb(var(--color-surface-50));
}

.radio-custom.disabled {
  border-color: rgb(var(--color-surface-300));
  background-color: rgb(var(--color-surface-100));
  cursor: not-allowed;
}

[data-theme="dark"] .radio-custom {
  border-color: rgb(var(--color-surface-500));
  background-color: rgb(var(--color-surface-800));
}

[data-theme="dark"] .radio-custom.checked {
  border-color: rgb(var(--color-primary-400));
  background-color: rgb(var(--color-surface-800));
}

[data-theme="dark"] .radio-custom.disabled {
  border-color: rgb(var(--color-surface-600));
  background-color: rgb(var(--color-surface-700));
}

/* Radio Dot */
.radio-dot {
  width: 0.5rem;
  height: 0.5rem;
  background-color: rgb(var(--color-primary-500));
  border-radius: 50%;
  transition: all 0.2s ease;
}

[data-theme="dark"] .radio-dot {
  background-color: rgb(var(--color-primary-400));
}

/* Radio Content */
.radio-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

/* Radio Text */
.radio-text {
  color: rgb(var(--color-surface-900));
  font-weight: 500;
}

.radio-text.disabled {
  color: rgb(var(--color-surface-500));
  cursor: not-allowed;
}

[data-theme="dark"] .radio-text {
  color: rgb(var(--color-surface-100));
}

[data-theme="dark"] .radio-text.disabled {
  color: rgb(var(--color-surface-400));
}

/* Radio Description */
.radio-description {
  color: rgb(var(--color-surface-600));
  font-size: 0.625rem;
  font-weight: 400;
  line-height: 1.3;
}

[data-theme="dark"] .radio-description {
  color: rgb(var(--color-surface-400));
}

/* Selection Display */
.radio-selection-display {
  padding-top: 0.5rem;
  border-top: 1px solid rgb(var(--color-surface-200));
  margin-top: 0.25rem;
}

[data-theme="dark"] .radio-selection-display {
  border-top-color: rgb(var(--color-surface-600));
}

.selection-display-text {
  font-size: 0.625rem;
  color: rgb(var(--color-surface-600));
  font-weight: 500;
}

[data-theme="dark"] .selection-display-text {
  color: rgb(var(--color-surface-400));
}

/* Dropdown Input Component Styles */
.dropdown-container {
  position: relative;
  width: 100%;
}

.dropdown-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid rgb(var(--color-surface-300));
  border-radius: 0.5rem;
  background-color: rgb(var(--color-surface-50));
  color: rgb(var(--color-surface-900));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  min-height: 2.5rem;
}

.dropdown-trigger:hover:not(.disabled) {
  border-color: rgb(var(--color-primary-400));
}

.dropdown-trigger:focus-within,
.dropdown-container.open .dropdown-trigger {
  border-color: rgb(var(--color-primary-500));
  box-shadow: 0 0 0 2px rgba(var(--color-primary-500), 0.2);
}

.dropdown-container.has-error .dropdown-trigger {
  border-color: #fca5a5;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
}

.dropdown-container.has-error:focus-within .dropdown-trigger,
.dropdown-container.has-error.open .dropdown-trigger {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.dropdown-container.disabled .dropdown-trigger {
  background-color: rgb(var(--color-surface-100));
  color: rgb(var(--color-surface-500));
  cursor: not-allowed;
}

[data-theme="dark"] .dropdown-trigger {
  border-color: rgb(var(--color-surface-600));
  background-color: rgb(var(--color-surface-800));
  color: rgb(var(--color-surface-100));
}

[data-theme="dark"] .dropdown-container.disabled .dropdown-trigger {
  background-color: rgb(var(--color-surface-700));
  color: rgb(var(--color-surface-400));
}

/* Dropdown Content */
.dropdown-content {
  flex: 1;
  display: flex;
  align-items: center;
  min-width: 0;
}

.dropdown-placeholder {
  color: rgb(var(--color-surface-400));
}

[data-theme="dark"] .dropdown-placeholder {
  color: rgb(var(--color-surface-400));
}

.dropdown-selected {
  color: rgb(var(--color-surface-900));
  font-weight: 500;
}

[data-theme="dark"] .dropdown-selected {
  color: rgb(var(--color-surface-100));
}

/* Tags for Multiple Selection */
.dropdown-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  width: 100%;
}

.dropdown-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.125rem 0.375rem;
  background-color: rgb(var(--color-primary-100));
  color: rgb(var(--color-primary-800));
  border-radius: 0.25rem;
  font-size: 0.625rem;
  font-weight: 500;
}

[data-theme="dark"] .dropdown-tag {
  background-color: rgb(var(--color-primary-900));
  color: rgb(var(--color-primary-200));
}

.tag-remove {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.tag-remove:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .tag-remove:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Dropdown Actions */
.dropdown-actions {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-left: 0.5rem;
}

.dropdown-clear {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: rgb(var(--color-surface-500));
  cursor: pointer;
  padding: 0.125rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.dropdown-clear:hover {
  color: rgb(var(--color-surface-700));
  background-color: rgb(var(--color-surface-100));
}

[data-theme="dark"] .dropdown-clear {
  color: rgb(var(--color-surface-400));
}

[data-theme="dark"] .dropdown-clear:hover {
  color: rgb(var(--color-surface-200));
  background-color: rgb(var(--color-surface-700));
}

.dropdown-chevron {
  color: rgb(var(--color-surface-500));
  transition: transform 0.2s ease;
}

.dropdown-chevron.open {
  transform: rotate(180deg);
}

[data-theme="dark"] .dropdown-chevron {
  color: rgb(var(--color-surface-400));
}

/* Dropdown Menu */
.dropdown-menu {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: rgb(var(--color-surface-50));
  border: 1px solid rgb(var(--color-surface-300));
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(var(--color-surface-900), 0.1), 0 4px 6px -2px rgba(var(--color-surface-900), 0.05);
  overflow: hidden;
}

[data-theme="dark"] .dropdown-menu {
  background-color: rgb(var(--color-surface-800));
  border-color: rgb(var(--color-surface-600));
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

/* Search */
.dropdown-search {
  padding: 0.75rem;
  border-bottom: 1px solid rgb(var(--color-surface-200));
}

[data-theme="dark"] .dropdown-search {
  border-bottom-color: rgb(var(--color-surface-600));
}

.dropdown-search-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid rgb(var(--color-surface-300));
  border-radius: 0.375rem;
  background-color: rgb(var(--color-surface-50));
  color: rgb(var(--color-surface-900));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  font-size: 0.75rem;
}

.dropdown-search-input:focus {
  outline: none;
  border-color: rgb(var(--color-primary-500));
  box-shadow: 0 0 0 2px rgba(var(--color-primary-500), 0.2);
}

[data-theme="dark"] .dropdown-search-input {
  border-color: rgb(var(--color-surface-600));
  background-color: rgb(var(--color-surface-700));
  color: rgb(var(--color-surface-100));
}

/* Select All */
.dropdown-select-all {
  border-bottom: 1px solid rgb(var(--color-surface-200));
}

[data-theme="dark"] .dropdown-select-all {
  border-bottom-color: rgb(var(--color-surface-600));
}

.select-all-option {
  font-weight: 600;
  color: rgb(var(--color-primary-600));
}

[data-theme="dark"] .select-all-option {
  color: rgb(var(--color-primary-400));
}

/* Options */
.dropdown-options {
  max-height: inherit;
  overflow-y: auto;
}

.option-group {
  /* Group styling if needed */
}

.group-header {
  padding: 0.5rem 0.75rem;
  font-size: 0.625rem;
  font-weight: 600;
  color: rgb(var(--color-surface-600));
  background-color: rgb(var(--color-surface-100));
  border-bottom: 1px solid rgb(var(--color-surface-200));
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

[data-theme="dark"] .group-header {
  color: rgb(var(--color-surface-400));
  background-color: rgb(var(--color-surface-700));
  border-bottom-color: rgb(var(--color-surface-600));
}

.dropdown-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0.75rem;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
}

.dropdown-option:hover:not(.disabled) {
  background-color: rgb(var(--color-surface-100));
}

.dropdown-option.selected {
  background-color: rgb(var(--color-primary-50));
  color: rgb(var(--color-primary-700));
}

.dropdown-option.disabled {
  color: rgb(var(--color-surface-400));
  cursor: not-allowed;
}

[data-theme="dark"] .dropdown-option:hover:not(.disabled) {
  background-color: rgb(var(--color-surface-700));
}

[data-theme="dark"] .dropdown-option.selected {
  background-color: rgb(var(--color-primary-900));
  color: rgb(var(--color-primary-300));
}

[data-theme="dark"] .dropdown-option.disabled {
  color: rgb(var(--color-surface-500));
}

.option-content {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
  flex: 1;
}

.option-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: rgb(var(--color-surface-900));
}

[data-theme="dark"] .option-label {
  color: rgb(var(--color-surface-100));
}

.option-description {
  font-size: 0.625rem;
  color: rgb(var(--color-surface-600));
  line-height: 1.3;
}

[data-theme="dark"] .option-description {
  color: rgb(var(--color-surface-400));
}

.option-check {
  color: rgb(var(--color-primary-600));
  margin-left: 0.5rem;
}

[data-theme="dark"] .option-check {
  color: rgb(var(--color-primary-400));
}

.dropdown-no-options {
  padding: 0.75rem;
  text-align: center;
  color: rgb(var(--color-surface-500));
  font-size: 0.75rem;
  font-style: italic;
}

[data-theme="dark"] .dropdown-no-options {
  color: rgb(var(--color-surface-400));
}

/* Textarea Input Component Styles */
.textarea-container {
  position: relative;
  width: 100%;
}

.textarea-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid rgb(var(--color-surface-300));
  border-radius: 0.5rem;
  background-color: rgb(var(--color-surface-50));
  color: rgb(var(--color-surface-900));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  font-size: 0.75rem;
  line-height: 1.5;
  transition: all 0.2s ease-in-out;
  outline: none;
  min-height: 6rem;
}

.textarea-input::placeholder {
  color: rgb(var(--color-surface-400));
}

.textarea-input:hover:not(:disabled):not(:read-only) {
  border-color: rgb(var(--color-primary-400));
}

.textarea-input:focus {
  border-color: rgb(var(--color-primary-500));
  box-shadow: 0 0 0 2px rgba(var(--color-primary-500), 0.2);
}

.textarea-container.has-error .textarea-input {
  border-color: #fca5a5;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
}

.textarea-container.has-error .textarea-input:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.textarea-input:disabled,
.textarea-input:read-only {
  background-color: rgb(var(--color-surface-100));
  color: rgb(var(--color-surface-500));
  cursor: not-allowed;
}

.textarea-container.disabled {
  cursor: not-allowed;
}

[data-theme="dark"] .textarea-input {
  border-color: rgb(var(--color-surface-600));
  background-color: rgb(var(--color-surface-800));
  color: rgb(var(--color-surface-100));
}

[data-theme="dark"] .textarea-input::placeholder {
  color: rgb(var(--color-surface-400));
}

[data-theme="dark"] .textarea-input:disabled,
[data-theme="dark"] .textarea-input:read-only {
  background-color: rgb(var(--color-surface-700));
  color: rgb(var(--color-surface-400));
}

/* Resize Options */
.textarea-resize-none {
  resize: none;
}

.textarea-resize-vertical {
  resize: vertical;
}

.textarea-resize-horizontal {
  resize: horizontal;
}

.textarea-resize-both {
  resize: both;
}

.textarea-input.auto-resize {
  resize: none;
  overflow: hidden;
}

/* Textarea Counts */
.textarea-counts {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 0.5rem 0.75rem;
  background-color: rgb(var(--color-surface-100));
  border: 1px solid rgb(var(--color-surface-300));
  border-top: none;
  border-radius: 0 0 0.5rem 0.5rem;
  font-size: 0.625rem;
}

[data-theme="dark"] .textarea-counts {
  background-color: rgb(var(--color-surface-700));
  border-color: rgb(var(--color-surface-600));
}

.count-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.count-label {
  color: rgb(var(--color-surface-600));
  font-weight: 500;
}

[data-theme="dark"] .count-label {
  color: rgb(var(--color-surface-400));
}

.count-value {
  color: rgb(var(--color-surface-800));
  font-weight: 600;
  font-variant-numeric: tabular-nums;
}

[data-theme="dark"] .count-value {
  color: rgb(var(--color-surface-200));
}

.count-value.count-error {
  color: #ef4444;
}

[data-theme="dark"] .count-value.count-error {
  color: #f87171;
}

/* Textarea with counts styling adjustments */
.textarea-container:has(.textarea-counts) .textarea-input {
  border-radius: 0.5rem 0.5rem 0 0;
}

/* Focus state for textarea with counts */
.textarea-container:has(.textarea-counts):focus-within .textarea-counts {
  border-color: rgb(var(--color-primary-500));
}

.textarea-container.has-error:has(.textarea-counts) .textarea-counts {
  border-color: #fca5a5;
}

.textarea-container.has-error:has(.textarea-counts):focus-within .textarea-counts {
  border-color: #ef4444;
}

/* Slider Input Component Styles */
.slider-container {
  position: relative;
  width: 100%;
  padding: 1rem 0;
}

.slider-container.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Min/Max Labels */
.slider-minmax {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.625rem;
  color: rgb(var(--color-surface-600));
}

[data-theme="dark"] .slider-minmax {
  color: rgb(var(--color-surface-400));
}

.slider-min-label,
.slider-max-label {
  font-weight: 500;
}

/* Slider Track */
.slider-track {
  position: relative;
  width: 100%;
  height: 0.5rem;
  cursor: pointer;
  border-radius: 0.25rem;
  outline: none;
}

.slider-container.vertical .slider-track {
  width: 0.5rem;
  height: 12rem;
  margin: 0 auto;
}

.slider-track:focus-visible {
  box-shadow: 0 0 0 2px rgba(var(--color-primary-500), 0.2);
}

/* Track Background */
.slider-track-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgb(var(--color-surface-300));
  border-radius: inherit;
  transition: background-color 0.2s ease;
}

[data-theme="dark"] .slider-track-bg {
  background-color: rgb(var(--color-surface-600));
}

.slider-container:hover:not(.disabled) .slider-track-bg {
  background-color: rgb(var(--color-surface-400));
}

[data-theme="dark"] .slider-container:hover:not(.disabled) .slider-track-bg {
  background-color: rgb(var(--color-surface-500));
}

/* Track Fill */
.slider-track-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: rgb(var(--color-primary-500));
  border-radius: inherit;
  transition: background-color 0.2s ease;
}

.slider-container.vertical .slider-track-fill {
  width: 100%;
  left: 0;
  bottom: 0;
  top: auto;
}

/* Color Variants */
.slider-color-primary .slider-track-fill {
  background-color: rgb(var(--color-primary-500));
}

.slider-color-secondary .slider-track-fill {
  background-color: rgb(var(--color-surface-700));
}

.slider-color-success .slider-track-fill {
  background-color: #10b981;
}

.slider-color-warning .slider-track-fill {
  background-color: #f59e0b;
}

.slider-color-error .slider-track-fill {
  background-color: #ef4444;
}

[data-theme="dark"] .slider-color-secondary .slider-track-fill {
  background-color: rgb(var(--color-surface-300));
}

/* Slider Thumb */
.slider-thumb {
  position: absolute;
  top: 50%;
  width: 1.25rem;
  height: 1.25rem;
  background-color: rgb(var(--color-primary-500));
  border: 2px solid white;
  border-radius: 50%;
  cursor: grab;
  transform: translate(-50%, -50%);
  transition: all 0.2s ease;
  outline: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider-container.vertical .slider-thumb {
  top: auto;
  left: 50%;
  transform: translate(-50%, 50%);
}

.slider-thumb:hover:not(.disabled),
.slider-thumb.active {
  transform: translate(-50%, -50%) scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.slider-container.vertical .slider-thumb:hover:not(.disabled),
.slider-container.vertical .slider-thumb.active {
  transform: translate(-50%, 50%) scale(1.1);
}

.slider-thumb:active {
  cursor: grabbing;
}

.slider-thumb:focus-visible {
  box-shadow: 0 0 0 2px rgba(var(--color-primary-500), 0.2), 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Thumb Color Variants */
.slider-color-primary .slider-thumb {
  background-color: rgb(var(--color-primary-500));
}

.slider-color-secondary .slider-thumb {
  background-color: rgb(var(--color-surface-700));
}

.slider-color-success .slider-thumb {
  background-color: #10b981;
}

.slider-color-warning .slider-thumb {
  background-color: #f59e0b;
}

.slider-color-error .slider-thumb {
  background-color: #ef4444;
}

[data-theme="dark"] .slider-color-secondary .slider-thumb {
  background-color: rgb(var(--color-surface-300));
}

[data-theme="dark"] .slider-thumb {
  border-color: rgb(var(--color-surface-800));
}

/* Size Variants */
.slider-size-small .slider-track {
  height: 0.375rem;
}

.slider-size-small .slider-thumb {
  width: 1rem;
  height: 1rem;
}

.slider-size-small.vertical .slider-track {
  width: 0.375rem;
  height: 8rem;
}

.slider-size-large .slider-track {
  height: 0.625rem;
}

.slider-size-large .slider-thumb {
  width: 1.5rem;
  height: 1.5rem;
}

.slider-size-large.vertical .slider-track {
  width: 0.625rem;
  height: 16rem;
}

/* Ticks */
.slider-tick {
  position: absolute;
  top: 50%;
  width: 0.125rem;
  height: 0.75rem;
  background-color: rgb(var(--color-surface-400));
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.slider-container.vertical .slider-tick {
  top: auto;
  left: 50%;
  width: 0.75rem;
  height: 0.125rem;
  transform: translate(-50%, 50%);
}

[data-theme="dark"] .slider-tick {
  background-color: rgb(var(--color-surface-500));
}

/* Marks */
.slider-mark {
  position: absolute;
  top: 100%;
  transform: translateX(-50%);
  pointer-events: none;
}

.slider-container.vertical .slider-mark {
  top: auto;
  left: 100%;
  transform: translateY(50%);
}

.slider-mark::before {
  content: '';
  position: absolute;
  top: -0.5rem;
  left: 50%;
  width: 0.25rem;
  height: 0.25rem;
  background-color: rgb(var(--color-surface-600));
  border-radius: 50%;
  transform: translateX(-50%);
}

.slider-container.vertical .slider-mark::before {
  top: 50%;
  left: -0.5rem;
  transform: translateY(-50%);
}

[data-theme="dark"] .slider-mark::before {
  background-color: rgb(var(--color-surface-400));
}

.slider-mark-label {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: rgb(var(--color-surface-600));
  white-space: nowrap;
}

.slider-container.vertical .slider-mark-label {
  margin-top: 0;
  margin-left: 0.5rem;
}

[data-theme="dark"] .slider-mark-label {
  color: rgb(var(--color-surface-400));
}

/* Value Display */
.slider-value {
  margin-top: 0.75rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 600;
  color: rgb(var(--color-surface-800));
  font-variant-numeric: tabular-nums;
}

[data-theme="dark"] .slider-value {
  color: rgb(var(--color-surface-200));
}

/* Error State */
.slider-container.has-error .slider-track-fill {
  background-color: #ef4444;
}

.slider-container.has-error .slider-thumb {
  background-color: #ef4444;
  border-color: #fca5a5;
}

/* Orientation Classes */
.slider-container.horizontal {
  /* Default horizontal styles already applied */
}

.slider-container.vertical {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: auto;
}

.slider-container.vertical .slider-minmax {
  flex-direction: column-reverse;
  height: 12rem;
  width: auto;
  margin-bottom: 0;
  margin-right: 0.5rem;
  justify-content: space-between;
}

.slider-container.vertical .slider-value {
  margin-top: 0.5rem;
}

/* Percentage Input Component Styles */
.percentage-input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  background-color: rgb(var(--color-surface-50));
  border: 1px solid rgb(var(--color-surface-300));
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
}

.percentage-input-container:hover:not(.disabled) {
  border-color: rgb(var(--color-surface-400));
}

.percentage-input-container.focused {
  border-color: rgb(var(--color-primary-500));
  box-shadow: 0 0 0 2px rgba(var(--color-primary-500), 0.2);
}

.percentage-input-container.has-error {
  border-color: #fca5a5;
}

.percentage-input-container.has-error.focused {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.percentage-input-container.disabled {
  background-color: rgb(var(--color-surface-100));
  border-color: rgb(var(--color-surface-200));
  opacity: 0.6;
  cursor: not-allowed;
}

[data-theme="dark"] .percentage-input-container {
  background-color: rgb(var(--color-surface-800));
  border-color: rgb(var(--color-surface-600));
}

[data-theme="dark"] .percentage-input-container:hover:not(.disabled) {
  border-color: rgb(var(--color-surface-500));
}

[data-theme="dark"] .percentage-input-container.disabled {
  background-color: rgb(var(--color-surface-900));
  border-color: rgb(var(--color-surface-700));
}

/* Percentage Input Field */
.percentage-input {
  flex: 1;
  width: 100%;
  padding: 0.75rem;
  padding-right: 2.5rem;
  border: none;
  background: transparent;
  color: rgb(var(--color-surface-900));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  font-size: 0.875rem;
  line-height: 1.5;
  outline: none;
  font-variant-numeric: tabular-nums;
  min-width: 0;
  text-align: left;
}

.percentage-input::placeholder {
  color: rgb(var(--color-surface-500));
}

.percentage-input:disabled {
  cursor: not-allowed;
}

[data-theme="dark"] .percentage-input {
  color: rgb(var(--color-surface-100));
}

[data-theme="dark"] .percentage-input::placeholder {
  color: rgb(var(--color-surface-400));
}

/* Percentage Icon */
.percentage-icon {
  position: absolute;
  right: 1.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: rgb(var(--color-surface-500));
  pointer-events: none;
  transition: color 0.2s ease;
  z-index: 1;
}

.percentage-input-container.focused .percentage-icon {
  color: rgb(var(--color-primary-500));
}

.percentage-input-container.has-error .percentage-icon {
  color: #ef4444;
}

[data-theme="dark"] .percentage-icon {
  color: rgb(var(--color-surface-400));
}

/* Percentage Mode Indicator */
.percentage-mode {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  z-index: 2;
  background-color: rgb(var(--color-surface-50));
  padding: 0 0.25rem;
  border-radius: 0.25rem;
}

.percentage-mode-text {
  font-size: 0.75rem;
  font-weight: 600;
  color: rgb(var(--color-surface-600));
  transition: color 0.2s ease;
  white-space: nowrap;
}

.percentage-input-container.focused .percentage-mode-text {
  color: rgb(var(--color-primary-500));
}

.percentage-input-container.has-error .percentage-mode-text {
  color: #ef4444;
}

[data-theme="dark"] .percentage-mode {
  background-color: rgb(var(--color-surface-800));
}

[data-theme="dark"] .percentage-mode-text {
  color: rgb(var(--color-surface-300));
}

/* Helper Text */
.percentage-helper {
  margin-top: 0.5rem;
}

.helper-text {
  font-size: 0.75rem;
  color: rgb(var(--color-surface-600));
  font-style: italic;
}

[data-theme="dark"] .helper-text {
  color: rgb(var(--color-surface-400));
}

/* Range Display */
.percentage-range {
  margin-top: 0.25rem;
}

.range-text {
  font-size: 0.75rem;
  color: rgb(var(--color-surface-600));
  font-weight: 500;
}

[data-theme="dark"] .range-text {
  color: rgb(var(--color-surface-400));
}

/* Percentage Input Variants */
.percentage-input-container.success {
  border-color: #10b981;
}

.percentage-input-container.success.focused {
  border-color: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.percentage-input-container.success .percentage-icon,
.percentage-input-container.success .percentage-mode-text {
  color: #10b981;
}

.percentage-input-container.warning {
  border-color: #f59e0b;
}

.percentage-input-container.warning.focused {
  border-color: #f59e0b;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
}

.percentage-input-container.warning .percentage-icon,
.percentage-input-container.warning .percentage-mode-text {
  color: #f59e0b;
}

/* Percentage Input Animations */

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Percentage Input Focus Ring */
.percentage-input:focus-visible {
  outline: none;
}

/* Percentage Input Number Formatting */
.percentage-input[inputmode="decimal"] {
  text-align: left;
  padding-right: 2.5rem;
}

/* Percentage Input Readonly State */
.percentage-input-container.readonly {
  background-color: rgb(var(--color-surface-100));
  border-style: dashed;
}

.percentage-input-container.readonly .percentage-input {
  cursor: default;
}

[data-theme="dark"] .percentage-input-container.readonly {
  background-color: rgb(var(--color-surface-900));
}

/* Date Input Component Styles */
.date-input-container {
  position: relative;
  display: flex;
  align-items: center;
  background-color: rgb(var(--color-surface-50));
  border: 1px solid rgb(var(--color-surface-300));
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
}

.date-input-container:hover:not(.disabled) {
  border-color: rgb(var(--color-surface-400));
}

.date-input-container.focused {
  border-color: rgb(var(--color-primary-500));
  box-shadow: 0 0 0 2px rgba(var(--color-primary-500), 0.2);
}

.date-input-container.has-error {
  border-color: #fca5a5;
}

.date-input-container.has-error.focused {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.date-input-container.disabled {
  background-color: rgb(var(--color-surface-100));
  border-color: rgb(var(--color-surface-200));
  opacity: 0.6;
  cursor: not-allowed;
}

[data-theme="dark"] .date-input-container {
  background-color: rgb(var(--color-surface-800));
  border-color: rgb(var(--color-surface-600));
}

[data-theme="dark"] .date-input-container:hover:not(.disabled) {
  border-color: rgb(var(--color-surface-500));
}

[data-theme="dark"] .date-input-container.disabled {
  background-color: rgb(var(--color-surface-900));
  border-color: rgb(var(--color-surface-700));
}

/* Date Input Field */
.date-input {
  flex: 1;
  padding: 0.75rem;
  padding-right: 2.5rem;
  border: none;
  background: transparent;
  color: rgb(var(--color-surface-900));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  font-size: 0.875rem;
  line-height: 1.5;
  outline: none;
  font-variant-numeric: tabular-nums;
}

.date-input::placeholder {
  color: rgb(var(--color-surface-500));
}

.date-input:disabled {
  cursor: not-allowed;
}

.date-input::-webkit-calendar-picker-indicator {
  opacity: 0;
  position: absolute;
  right: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.date-input::-webkit-inner-spin-button,
.date-input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

[data-theme="dark"] .date-input {
  color: rgb(var(--color-surface-100));
}

[data-theme="dark"] .date-input::placeholder {
  color: rgb(var(--color-surface-400));
}

/* Date Icon */
.date-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: rgb(var(--color-surface-500));
  pointer-events: none;
  transition: color 0.2s ease;
}

.date-input-container.focused .date-icon {
  color: rgb(var(--color-primary-500));
}

.date-input-container.has-error .date-icon {
  color: #ef4444;
}

[data-theme="dark"] .date-icon {
  color: rgb(var(--color-surface-400));
}

/* Date Helper Text */
.date-helper {
  margin-top: 0.5rem;
}

.date-helper .helper-text {
  font-size: 0.75rem;
  color: rgb(var(--color-surface-600));
  font-style: italic;
}

[data-theme="dark"] .date-helper .helper-text {
  color: rgb(var(--color-surface-400));
}

/* DateTime Input Component Styles */
.datetime-input-container {
  position: relative;
  display: flex;
  align-items: center;
  background-color: rgb(var(--color-surface-50));
  border: 1px solid rgb(var(--color-surface-300));
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
}

.datetime-input-container:hover:not(.disabled) {
  border-color: rgb(var(--color-surface-400));
}

.datetime-input-container.focused {
  border-color: rgb(var(--color-primary-500));
  box-shadow: 0 0 0 2px rgba(var(--color-primary-500), 0.2);
}

.datetime-input-container.has-error {
  border-color: #fca5a5;
}

.datetime-input-container.has-error.focused {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.datetime-input-container.disabled {
  background-color: rgb(var(--color-surface-100));
  border-color: rgb(var(--color-surface-200));
  opacity: 0.6;
  cursor: not-allowed;
}

[data-theme="dark"] .datetime-input-container {
  background-color: rgb(var(--color-surface-800));
  border-color: rgb(var(--color-surface-600));
}

[data-theme="dark"] .datetime-input-container:hover:not(.disabled) {
  border-color: rgb(var(--color-surface-500));
}

[data-theme="dark"] .datetime-input-container.disabled {
  background-color: rgb(var(--color-surface-900));
  border-color: rgb(var(--color-surface-700));
}

/* DateTime Input Field */
.datetime-input {
  flex: 1;
  padding: 0.75rem;
  padding-right: 3rem;
  border: none;
  background: transparent;
  color: rgb(var(--color-surface-900));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  font-size: 0.875rem;
  line-height: 1.5;
  outline: none;
  font-variant-numeric: tabular-nums;
}

.datetime-input::placeholder {
  color: rgb(var(--color-surface-500));
}

.datetime-input:disabled {
  cursor: not-allowed;
}

.datetime-input::-webkit-calendar-picker-indicator {
  opacity: 0;
  position: absolute;
  right: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.datetime-input::-webkit-inner-spin-button,
.datetime-input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

[data-theme="dark"] .datetime-input {
  color: rgb(var(--color-surface-100));
}

[data-theme="dark"] .datetime-input::placeholder {
  color: rgb(var(--color-surface-400));
}

/* DateTime Icon */
.datetime-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: rgb(var(--color-surface-500));
  pointer-events: none;
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.datetime-input-container.focused .datetime-icon {
  color: rgb(var(--color-primary-500));
}

.datetime-input-container.has-error .datetime-icon {
  color: #ef4444;
}

[data-theme="dark"] .datetime-icon {
  color: rgb(var(--color-surface-400));
}

/* DateTime Helper Text */
.datetime-helper {
  margin-top: 0.5rem;
}

.datetime-helper .helper-text {
  font-size: 0.75rem;
  color: rgb(var(--color-surface-600));
  font-style: italic;
}

[data-theme="dark"] .datetime-helper .helper-text {
  color: rgb(var(--color-surface-400));
}

/* Email Input Component Styles */
.email-input-container {
  position: relative;
  display: flex;
  align-items: center;
  background-color: rgb(var(--color-surface-50));
  border: 1px solid rgb(var(--color-surface-300));
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
}

.email-input-container:hover:not(.disabled) {
  border-color: rgb(var(--color-surface-400));
}

.email-input-container.focused {
  border-color: rgb(var(--color-primary-500));
  box-shadow: 0 0 0 2px rgba(var(--color-primary-500), 0.2);
}

.email-input-container.has-error {
  border-color: #fca5a5;
}

.email-input-container.has-error.focused {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.email-input-container.is-valid {
  border-color: #10b981;
}

.email-input-container.is-valid.focused {
  border-color: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.email-input-container.disabled {
  background-color: rgb(var(--color-surface-100));
  border-color: rgb(var(--color-surface-200));
  opacity: 0.6;
  cursor: not-allowed;
}

[data-theme="dark"] .email-input-container {
  background-color: rgb(var(--color-surface-800));
  border-color: rgb(var(--color-surface-600));
}

[data-theme="dark"] .email-input-container:hover:not(.disabled) {
  border-color: rgb(var(--color-surface-500));
}

[data-theme="dark"] .email-input-container.disabled {
  background-color: rgb(var(--color-surface-900));
  border-color: rgb(var(--color-surface-700));
}

/* Email Input Field */
.email-input {
  flex: 1;
  padding: 0.75rem;
  padding-right: 3rem;
  border: none;
  background: transparent;
  color: rgb(var(--color-surface-900));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  font-size: 0.875rem;
  line-height: 1.5;
  outline: none;
}

.email-input::placeholder {
  color: rgb(var(--color-surface-500));
}

.email-input:disabled {
  cursor: not-allowed;
}

[data-theme="dark"] .email-input {
  color: rgb(var(--color-surface-100));
}

[data-theme="dark"] .email-input::placeholder {
  color: rgb(var(--color-surface-400));
}

/* Email Icon */
.email-icon {
  position: absolute;
  right: 2.5rem;
  top: 50%;
  transform: translateY(-50%);
  color: rgb(var(--color-surface-500));
  pointer-events: none;
  transition: color 0.2s ease;
}

.email-input-container.focused .email-icon {
  color: rgb(var(--color-primary-500));
}

.email-input-container.has-error .email-icon {
  color: #ef4444;
}

.email-input-container.is-valid .email-icon {
  color: #10b981;
}

[data-theme="dark"] .email-icon {
  color: rgb(var(--color-surface-400));
}

/* Validation Icon */
.validation-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  transition: all 0.2s ease;
}

.validation-icon.success {
  color: #10b981;
}

.validation-icon.error {
  color: #ef4444;
}

/* Email Helper Text */
.email-helper {
  margin-top: 0.5rem;
}

.email-helper .helper-text {
  font-size: 0.75rem;
  color: rgb(var(--color-surface-600));
  font-style: italic;
  display: block;
  margin-bottom: 0.25rem;
}

.email-helper .helper-text:last-child {
  margin-bottom: 0;
}

[data-theme="dark"] .email-helper .helper-text {
  color: rgb(var(--color-surface-400));
}

/* Email Input Animations */
.validation-icon {
  animation: fadeInScale 0.2s ease-in-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: translateY(-50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
}

/* Email Input Focus Ring */
.email-input:focus-visible {
  outline: none;
}

/* Email Input Readonly State */
.email-input-container.readonly {
  background-color: rgb(var(--color-surface-100));
  border-style: dashed;
}

.email-input-container.readonly .email-input {
  cursor: default;
}

[data-theme="dark"] .email-input-container.readonly {
  background-color: rgb(var(--color-surface-900));
}

/* Phone Input Component Styles */
.phone-input-container {
  position: relative;
  display: flex;
  align-items: center;
  background-color: rgb(var(--color-surface-50));
  border: 1px solid rgb(var(--color-surface-300));
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
}

.phone-input-container:hover:not(.disabled) {
  border-color: rgb(var(--color-surface-400));
}

.phone-input-container.focused {
  border-color: rgb(var(--color-primary-500));
  box-shadow: 0 0 0 2px rgba(var(--color-primary-500), 0.2);
}

.phone-input-container.has-error {
  border-color: #fca5a5;
}

.phone-input-container.has-error.focused {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.phone-input-container.is-valid {
  border-color: #10b981;
}

.phone-input-container.is-valid.focused {
  border-color: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.phone-input-container.disabled {
  background-color: rgb(var(--color-surface-100));
  border-color: rgb(var(--color-surface-200));
  opacity: 0.6;
  cursor: not-allowed;
}

[data-theme="dark"] .phone-input-container {
  background-color: rgb(var(--color-surface-800));
  border-color: rgb(var(--color-surface-600));
}

[data-theme="dark"] .phone-input-container:hover:not(.disabled) {
  border-color: rgb(var(--color-surface-500));
}

[data-theme="dark"] .phone-input-container.disabled {
  background-color: rgb(var(--color-surface-900));
  border-color: rgb(var(--color-surface-700));
}

/* Country Dropdown */
.phone-country-dropdown {
  position: relative;
  border-right: 1px solid rgb(var(--color-surface-300));
}

[data-theme="dark"] .phone-country-dropdown {
  border-right-color: rgb(var(--color-surface-600));
}

.country-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: transparent;
  border: none;
  color: rgb(var(--color-surface-900));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
}

.country-selector:hover:not(:disabled) {
  background-color: rgb(var(--color-surface-100));
}

.country-selector:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

[data-theme="dark"] .country-selector {
  color: rgb(var(--color-surface-100));
}

[data-theme="dark"] .country-selector:hover:not(:disabled) {
  background-color: rgb(var(--color-surface-700));
}

.country-flag {
  font-size: 1.2rem;
  line-height: 1;
}

.country-code {
  font-weight: 500;
  font-variant-numeric: tabular-nums;
}

.dropdown-icon {
  transition: transform 0.2s ease;
  color: rgb(var(--color-surface-500));
}

.dropdown-icon.open {
  transform: rotate(180deg);
}

[data-theme="dark"] .dropdown-icon {
  color: rgb(var(--color-surface-400));
}

/* Country Dropdown Menu */
.country-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: rgb(var(--color-surface-50));
  border: 1px solid rgb(var(--color-surface-300));
  border-radius: 0.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  max-height: 300px;
  overflow: hidden;
  margin-top: 0.25rem;
}

[data-theme="dark"] .country-dropdown-menu {
  background-color: rgb(var(--color-surface-800));
  border-color: rgb(var(--color-surface-600));
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.country-search {
  padding: 0.75rem;
  border-bottom: 1px solid rgb(var(--color-surface-200));
}

[data-theme="dark"] .country-search {
  border-bottom-color: rgb(var(--color-surface-700));
}

.country-search-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid rgb(var(--color-surface-300));
  border-radius: 0.375rem;
  background-color: rgb(var(--color-surface-50));
  color: rgb(var(--color-surface-900));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  font-size: 0.75rem;
  outline: none;
  transition: border-color 0.2s ease;
}

.country-search-input:focus {
  border-color: rgb(var(--color-primary-500));
}

[data-theme="dark"] .country-search-input {
  background-color: rgb(var(--color-surface-700));
  border-color: rgb(var(--color-surface-600));
  color: rgb(var(--color-surface-100));
}

.country-list {
  max-height: 200px;
  overflow-y: auto;
}

.country-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem;
  background: transparent;
  border: none;
  color: rgb(var(--color-surface-900));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  font-size: 0.75rem;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.country-option:hover {
  background-color: rgb(var(--color-surface-100));
}

.country-option.selected {
  background-color: rgb(var(--color-primary-100));
  color: rgb(var(--color-primary-900));
}

[data-theme="dark"] .country-option {
  color: rgb(var(--color-surface-100));
}

[data-theme="dark"] .country-option:hover {
  background-color: rgb(var(--color-surface-700));
}

[data-theme="dark"] .country-option.selected {
  background-color: rgb(var(--color-primary-900));
  color: rgb(var(--color-primary-100));
}

.country-name {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.country-dial-code {
  font-weight: 500;
  font-variant-numeric: tabular-nums;
  color: rgb(var(--color-surface-600));
}

[data-theme="dark"] .country-dial-code {
  color: rgb(var(--color-surface-400));
}

/* Phone Input Field */
.phone-input {
  flex: 1;
  padding: 0.75rem;
  padding-right: 3rem;
  border: none;
  background: transparent;
  color: rgb(var(--color-surface-900));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  font-size: 0.75rem;
  line-height: 1.5;
  outline: none;
  font-variant-numeric: tabular-nums;
}

.phone-input::placeholder {
  color: rgb(var(--color-surface-500));
}

.phone-input:disabled {
  cursor: not-allowed;
}

[data-theme="dark"] .phone-input {
  color: rgb(var(--color-surface-100));
}

[data-theme="dark"] .phone-input::placeholder {
  color: rgb(var(--color-surface-400));
}

/* Phone Icon */
.phone-icon {
  position: absolute;
  right: 2.5rem;
  top: 50%;
  transform: translateY(-50%);
  color: rgb(var(--color-surface-500));
  pointer-events: none;
  transition: color 0.2s ease;
}

.phone-input-container.focused .phone-icon {
  color: rgb(var(--color-primary-500));
}

.phone-input-container.has-error .phone-icon {
  color: #ef4444;
}

.phone-input-container.is-valid .phone-icon {
  color: #10b981;
}

[data-theme="dark"] .phone-icon {
  color: rgb(var(--color-surface-400));
}

/* Phone Helper Text */
.phone-helper {
  margin-top: 0.5rem;
}

.phone-helper .helper-text {
  font-size: 0.625rem;
  color: rgb(var(--color-surface-600));
  font-style: italic;
  display: block;
  margin-bottom: 0.25rem;
}

.phone-helper .helper-text:last-child {
  margin-bottom: 0;
}

[data-theme="dark"] .phone-helper .helper-text {
  color: rgb(var(--color-surface-400));
}

/* Dropdown Backdrop */
.dropdown-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: transparent;
}

/* Phone Input Animations */
.country-dropdown-menu {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Phone Input Readonly State */
.phone-input-container.readonly {
  background-color: rgb(var(--color-surface-100));
  border-style: dashed;
}

.phone-input-container.readonly .phone-input {
  cursor: default;
}

.phone-input-container.readonly .country-selector {
  cursor: default;
}

[data-theme="dark"] .phone-input-container.readonly {
  background-color: rgb(var(--color-surface-900));
}

/* Rich Text Input Component Styles */
.rich-text-container {
  background-color: rgb(var(--color-surface-50));
  border: 1px solid rgb(var(--color-surface-300));
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
  overflow: hidden;
}

.rich-text-container:hover:not(.disabled) {
  border-color: rgb(var(--color-surface-400));
}

.rich-text-container.focused {
  border-color: rgb(var(--color-primary-500));
  box-shadow: 0 0 0 2px rgba(var(--color-primary-500), 0.2);
}

.rich-text-container.has-error {
  border-color: #fca5a5;
}

.rich-text-container.has-error.focused {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.rich-text-container.disabled {
  background-color: rgb(var(--color-surface-100));
  border-color: rgb(var(--color-surface-200));
  opacity: 0.6;
  cursor: not-allowed;
}

[data-theme="dark"] .rich-text-container {
  background-color: rgb(var(--color-surface-800));
  border-color: rgb(var(--color-surface-600));
}

[data-theme="dark"] .rich-text-container:hover:not(.disabled) {
  border-color: rgb(var(--color-surface-500));
}

[data-theme="dark"] .rich-text-container.disabled {
  background-color: rgb(var(--color-surface-900));
  border-color: rgb(var(--color-surface-700));
}

/* Rich Text Toolbar */
.rich-text-toolbar {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  padding: 0.75rem;
  border-bottom: 1px solid rgb(var(--color-surface-200));
  background-color: rgb(var(--color-surface-25));
}

[data-theme="dark"] .rich-text-toolbar {
  border-bottom-color: rgb(var(--color-surface-700));
  background-color: rgb(var(--color-surface-850));
}

.toolbar-group {
  display: flex;
  gap: 0.125rem;
  padding: 0 0.25rem;
  border-right: 1px solid rgb(var(--color-surface-200));
}

.toolbar-group:last-child {
  border-right: none;
}

[data-theme="dark"] .toolbar-group {
  border-right-color: rgb(var(--color-surface-700));
}

.toolbar-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border: none;
  border-radius: 0.25rem;
  background-color: transparent;
  color: rgb(var(--color-surface-700));
  cursor: pointer;
  transition: all 0.2s ease;
}

.toolbar-button:hover:not(:disabled) {
  background-color: rgb(var(--color-surface-100));
  color: rgb(var(--color-surface-900));
}

.toolbar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-button.active {
  background-color: rgb(var(--color-primary-100));
  color: rgb(var(--color-primary-700));
}

[data-theme="dark"] .toolbar-button {
  color: rgb(var(--color-surface-300));
}

[data-theme="dark"] .toolbar-button:hover:not(:disabled) {
  background-color: rgb(var(--color-surface-700));
  color: rgb(var(--color-surface-100));
}

[data-theme="dark"] .toolbar-button.active {
  background-color: rgb(var(--color-primary-900));
  color: rgb(var(--color-primary-100));
}

/* Rich Text Content */
.rich-text-content {
  padding: 0.75rem;
  min-height: var(--rich-text-height, 200px);
}

.rich-text-content .ProseMirror {
  outline: none;
  color: rgb(var(--color-surface-900));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  font-size: 0.75rem;
  line-height: 1.6;
}

.rich-text-content .ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: rgb(var(--color-surface-500));
  pointer-events: none;
  height: 0;
}

[data-theme="dark"] .rich-text-content .ProseMirror {
  color: rgb(var(--color-surface-100));
}

[data-theme="dark"] .rich-text-content .ProseMirror p.is-editor-empty:first-child::before {
  color: rgb(var(--color-surface-400));
}

/* Rich Text Typography - Lighter Font Weights */
.rich-text-content .ProseMirror h1 {
  font-size: 1.25rem;
  font-weight: 500;
  margin: 1rem 0 0.5rem 0;
  line-height: 1.2;
}

.rich-text-content .ProseMirror h2 {
  font-size: 1.125rem;
  font-weight: 500;
  margin: 0.875rem 0 0.5rem 0;
  line-height: 1.3;
}

.rich-text-content .ProseMirror h3 {
  font-size: 1rem;
  font-weight: 500;
  margin: 0.75rem 0 0.5rem 0;
  line-height: 1.4;
}

.rich-text-content .ProseMirror p {
  margin: 0.5rem 0;
}

.rich-text-content .ProseMirror ul,
.rich-text-content .ProseMirror ol {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.rich-text-content .ProseMirror li {
  margin: 0.25rem 0;
}

.rich-text-content .ProseMirror blockquote {
  border-left: 4px solid rgb(var(--color-surface-300));
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: rgb(var(--color-surface-600));
}

[data-theme="dark"] .rich-text-content .ProseMirror blockquote {
  border-left-color: rgb(var(--color-surface-600));
  color: rgb(var(--color-surface-400));
}

.rich-text-content .ProseMirror code {
  background-color: rgb(var(--color-surface-100));
  color: rgb(var(--color-surface-800));
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.75rem;
}

[data-theme="dark"] .rich-text-content .ProseMirror code {
  background-color: rgb(var(--color-surface-700));
  color: rgb(var(--color-surface-200));
}

.rich-text-content .ProseMirror pre {
  background-color: rgb(var(--color-surface-100));
  color: rgb(var(--color-surface-800));
  padding: 1rem;
  border-radius: 0.5rem;
  margin: 1rem 0;
  overflow-x: auto;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.75rem;
  line-height: 1.4;
}

[data-theme="dark"] .rich-text-content .ProseMirror pre {
  background-color: rgb(var(--color-surface-700));
  color: rgb(var(--color-surface-200));
}

.rich-text-content .ProseMirror .rich-text-link {
  color: rgb(var(--color-primary-600));
  text-decoration: underline;
  cursor: pointer;
}

.rich-text-content .ProseMirror .rich-text-link:hover {
  color: rgb(var(--color-primary-700));
}

[data-theme="dark"] .rich-text-content .ProseMirror .rich-text-link {
  color: rgb(var(--color-primary-400));
}

[data-theme="dark"] .rich-text-content .ProseMirror .rich-text-link:hover {
  color: rgb(var(--color-primary-300));
}

/* Task Lists */
.rich-text-content .ProseMirror ul[data-type="taskList"] {
  list-style: none;
  padding-left: 0;
}

.rich-text-content .ProseMirror ul[data-type="taskList"] li {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.rich-text-content .ProseMirror ul[data-type="taskList"] li > label {
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.rich-text-content .ProseMirror ul[data-type="taskList"] li > div {
  flex: 1;
}

/* Rich Text Footer */
.rich-text-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-top: 1px solid rgb(var(--color-surface-200));
  background-color: rgb(var(--color-surface-25));
  font-size: 0.625rem;
  color: rgb(var(--color-surface-600));
}

[data-theme="dark"] .rich-text-footer {
  border-top-color: rgb(var(--color-surface-700));
  background-color: rgb(var(--color-surface-850));
  color: rgb(var(--color-surface-400));
}

.character-count.over-limit {
  color: #ef4444;
  font-weight: 500;
}

.word-count {
  color: rgb(var(--color-surface-600));
}

[data-theme="dark"] .word-count {
  color: rgb(var(--color-surface-400));
}

/* Rich Text Readonly State */
.rich-text-container.readonly {
  background-color: rgb(var(--color-surface-100));
  border-style: dashed;
}

.rich-text-container.readonly .rich-text-content .ProseMirror {
  cursor: default;
}

[data-theme="dark"] .rich-text-container.readonly {
  background-color: rgb(var(--color-surface-900));
}

/* File Upload Components Styles */

/* Common File Input Styles */
.file-input-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.hidden {
  display: none;
}

/* File Drop Zone */
.file-drop-zone,
.image-drop-zone,
.video-drop-zone {
  border: 2px dashed rgb(var(--color-surface-300));
  border-radius: 0.75rem;
  background-color: rgb(var(--color-surface-50));
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-drop-zone:hover:not(.disabled):not(.readonly),
.image-drop-zone:hover:not(.disabled):not(.readonly),
.video-drop-zone:hover:not(.disabled):not(.readonly) {
  border-color: rgb(var(--color-primary-400));
  background-color: rgb(var(--color-primary-50));
}

.file-drop-zone.drag-over,
.image-drop-zone.drag-over,
.video-drop-zone.drag-over {
  border-color: rgb(var(--color-primary-500));
  background-color: rgb(var(--color-primary-100));
  transform: scale(1.02);
}

.file-drop-zone.has-error,
.image-drop-zone.has-error,
.video-drop-zone.has-error {
  border-color: #fca5a5;
  background-color: rgba(239, 68, 68, 0.05);
}

.file-drop-zone.disabled,
.image-drop-zone.disabled,
.video-drop-zone.disabled {
  background-color: rgb(var(--color-surface-100));
  border-color: rgb(var(--color-surface-200));
  cursor: not-allowed;
  opacity: 0.6;
}

.file-drop-zone.readonly,
.image-drop-zone.readonly,
.video-drop-zone.readonly {
  background-color: rgb(var(--color-surface-100));
  border-style: solid;
  cursor: default;
}

[data-theme="dark"] .file-drop-zone,
[data-theme="dark"] .image-drop-zone,
[data-theme="dark"] .video-drop-zone {
  border-color: rgb(var(--color-surface-600));
  background-color: rgb(var(--color-surface-800));
}

[data-theme="dark"] .file-drop-zone:hover:not(.disabled):not(.readonly),
[data-theme="dark"] .image-drop-zone:hover:not(.disabled):not(.readonly),
[data-theme="dark"] .video-drop-zone:hover:not(.disabled):not(.readonly) {
  border-color: rgb(var(--color-primary-400));
  background-color: rgb(var(--color-primary-900));
}

[data-theme="dark"] .file-drop-zone.drag-over,
[data-theme="dark"] .image-drop-zone.drag-over,
[data-theme="dark"] .video-drop-zone.drag-over {
  background-color: rgb(var(--color-primary-800));
}

[data-theme="dark"] .file-drop-zone.disabled,
[data-theme="dark"] .image-drop-zone.disabled,
[data-theme="dark"] .video-drop-zone.disabled {
  background-color: rgb(var(--color-surface-900));
  border-color: rgb(var(--color-surface-700));
}

[data-theme="dark"] .file-drop-zone.readonly,
[data-theme="dark"] .image-drop-zone.readonly,
[data-theme="dark"] .video-drop-zone.readonly {
  background-color: rgb(var(--color-surface-900));
}

/* File Drop Content */
.file-drop-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.file-upload-icon {
  color: rgb(var(--color-surface-400));
  transition: color 0.3s ease;
}

.file-drop-zone:hover:not(.disabled):not(.readonly) .file-upload-icon,
.image-drop-zone:hover:not(.disabled):not(.readonly) .file-upload-icon,
.video-drop-zone:hover:not(.disabled):not(.readonly) .file-upload-icon {
  color: rgb(var(--color-primary-500));
}

.file-drop-text {
  font-size: 1rem;
  font-weight: 500;
  color: rgb(var(--color-surface-700));
  margin: 0;
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
}

.file-drop-hint {
  font-size: 0.875rem;
  color: rgb(var(--color-surface-500));
  margin: 0;
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
}

[data-theme="dark"] .file-drop-text {
  color: rgb(var(--color-surface-200));
}

[data-theme="dark"] .file-drop-hint {
  color: rgb(var(--color-surface-400));
}

/* File List */
.file-list {
  margin-top: 1rem;
  border: 1px solid rgb(var(--color-surface-200));
  border-radius: 0.5rem;
  background-color: rgb(var(--color-surface-25));
  overflow: hidden;
}

[data-theme="dark"] .file-list {
  border-color: rgb(var(--color-surface-700));
  background-color: rgb(var(--color-surface-850));
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid rgb(var(--color-surface-200));
  transition: background-color 0.2s ease;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item:hover {
  background-color: rgb(var(--color-surface-100));
}

[data-theme="dark"] .file-item {
  border-bottom-color: rgb(var(--color-surface-700));
}

[data-theme="dark"] .file-item:hover {
  background-color: rgb(var(--color-surface-800));
}

.file-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
  min-width: 0;
}

.file-icon {
  color: rgb(var(--color-surface-500));
  flex-shrink: 0;
}

[data-theme="dark"] .file-icon {
  color: rgb(var(--color-surface-400));
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  display: block;
  font-weight: 500;
  color: rgb(var(--color-surface-900));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

[data-theme="dark"] .file-name {
  color: rgb(var(--color-surface-100));
}

.file-meta {
  display: flex;
  gap: 0.75rem;
  margin-top: 0.25rem;
}

.file-size,
.file-type {
  font-size: 0.625rem;
  color: rgb(var(--color-surface-500));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
}

[data-theme="dark"] .file-size,
[data-theme="dark"] .file-type {
  color: rgb(var(--color-surface-400));
}

.file-actions {
  display: flex;
  gap: 0.25rem;
  align-items: center;
}

.file-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border: none;
  border-radius: 0.25rem;
  background-color: transparent;
  color: rgb(var(--color-surface-600));
  cursor: pointer;
  transition: all 0.2s ease;
}

.file-action-button:hover {
  background-color: rgb(var(--color-surface-200));
  color: rgb(var(--color-surface-800));
}

.file-action-button.remove:hover {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

[data-theme="dark"] .file-action-button {
  color: rgb(var(--color-surface-400));
}

[data-theme="dark"] .file-action-button:hover {
  background-color: rgb(var(--color-surface-700));
  color: rgb(var(--color-surface-200));
}

[data-theme="dark"] .file-action-button.remove:hover {
  background-color: rgba(239, 68, 68, 0.2);
  color: #f87171;
}

.file-total-size {
  padding: 0.75rem 1rem;
  background-color: rgb(var(--color-surface-100));
  border-top: 1px solid rgb(var(--color-surface-200));
  font-size: 0.75rem;
  font-weight: 500;
  color: rgb(var(--color-surface-700));
  text-align: center;
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
}

[data-theme="dark"] .file-total-size {
  background-color: rgb(var(--color-surface-800));
  border-top-color: rgb(var(--color-surface-700));
  color: rgb(var(--color-surface-300));
}

/* Image Input Specific Styles */
.image-preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.image-preview-item {
  position: relative;
  border: 1px solid rgb(var(--color-surface-200));
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: rgb(var(--color-surface-25));
  transition: all 0.2s ease;
}

.image-preview-item:hover {
  border-color: rgb(var(--color-primary-300));
  box-shadow: 0 4px 12px rgba(var(--color-primary-500), 0.15);
}

[data-theme="dark"] .image-preview-item {
  border-color: rgb(var(--color-surface-700));
  background-color: rgb(var(--color-surface-850));
}

[data-theme="dark"] .image-preview-item:hover {
  border-color: rgb(var(--color-primary-600));
}

.image-preview-container {
  position: relative;
  overflow: hidden;
}

.image-preview {
  width: 100%;
  height: 150px;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.image-preview:hover {
  transform: scale(1.05);
}

.image-preview-small .image-preview {
  height: 100px;
}

.image-preview-medium .image-preview {
  height: 150px;
}

.image-preview-large .image-preview {
  height: 200px;
}

.image-actions-overlay {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.image-preview-container:hover .image-actions-overlay {
  opacity: 1;
}

.image-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.75rem;
  height: 1.75rem;
  border: none;
  border-radius: 0.25rem;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.image-action-button:hover {
  background-color: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}

.image-action-button.remove:hover {
  background-color: rgba(239, 68, 68, 0.9);
}

.image-info {
  padding: 0.75rem;
}

.image-name {
  font-weight: 500;
  color: rgb(var(--color-surface-900));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 0.25rem;
}

[data-theme="dark"] .image-name {
  color: rgb(var(--color-surface-100));
}

.image-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  font-size: 0.625rem;
  color: rgb(var(--color-surface-500));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
}

[data-theme="dark"] .image-meta {
  color: rgb(var(--color-surface-400));
}

.image-size,
.image-dimensions,
.image-megapixels {
  background-color: rgb(var(--color-surface-100));
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.625rem;
}

[data-theme="dark"] .image-size,
[data-theme="dark"] .image-dimensions,
[data-theme="dark"] .image-megapixels {
  background-color: rgb(var(--color-surface-700));
}

/* Image Modal */
.image-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.image-modal-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  background-color: rgb(var(--color-surface-50));
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

[data-theme="dark"] .image-modal-content {
  background-color: rgb(var(--color-surface-800));
}

.image-modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 2.5rem;
  height: 2.5rem;
  border: none;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 10;
}

.image-modal-close:hover {
  background-color: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}

.image-modal-image {
  max-width: 100%;
  max-height: 90vh;
  object-fit: contain;
  display: block;
}

/* Video Input Specific Styles */
.video-preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.video-preview-item {
  position: relative;
  border: 1px solid rgb(var(--color-surface-200));
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: rgb(var(--color-surface-25));
  transition: all 0.2s ease;
}

.video-preview-item:hover {
  border-color: rgb(var(--color-primary-300));
  box-shadow: 0 4px 12px rgba(var(--color-primary-500), 0.15);
}

[data-theme="dark"] .video-preview-item {
  border-color: rgb(var(--color-surface-700));
  background-color: rgb(var(--color-surface-850));
}

[data-theme="dark"] .video-preview-item:hover {
  border-color: rgb(var(--color-primary-600));
}

.video-preview-container {
  position: relative;
  overflow: hidden;
}

.video-preview {
  width: 100%;
  height: 200px;
  object-fit: cover;
  background-color: #000;
  border-radius: 0.25rem 0.25rem 0 0;
}

.video-preview-small .video-preview {
  height: 150px;
}

.video-preview-medium .video-preview {
  height: 200px;
}

.video-preview-large .video-preview {
  height: 250px;
}

.video-actions-overlay {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.video-preview-container:hover .video-actions-overlay {
  opacity: 1;
}

.video-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.75rem;
  height: 1.75rem;
  border: none;
  border-radius: 0.25rem;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.video-action-button:hover {
  background-color: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}

.video-action-button.play {
  background-color: rgba(var(--color-primary-500), 0.9);
}

.video-action-button.play:hover {
  background-color: rgba(var(--color-primary-600), 0.9);
}

.video-action-button.remove:hover {
  background-color: rgba(239, 68, 68, 0.9);
}

.video-info {
  padding: 0.75rem;
}

.video-name {
  font-weight: 500;
  color: rgb(var(--color-surface-900));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 0.25rem;
}

[data-theme="dark"] .video-name {
  color: rgb(var(--color-surface-100));
}

.video-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  font-size: 0.625rem;
  color: rgb(var(--color-surface-500));
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
}

[data-theme="dark"] .video-meta {
  color: rgb(var(--color-surface-400));
}

.video-size,
.video-duration,
.video-dimensions {
  background-color: rgb(var(--color-surface-100));
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.625rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

[data-theme="dark"] .video-size,
[data-theme="dark"] .video-duration,
[data-theme="dark"] .video-dimensions {
  background-color: rgb(var(--color-surface-700));
}

.video-duration svg {
  width: 12px;
  height: 12px;
}

/* Responsive Design for File Uploads */
@media (max-width: 768px) {
  .image-preview-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 0.75rem;
  }

  .video-preview-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .file-drop-zone,
  .image-drop-zone,
  .video-drop-zone {
    padding: 1.5rem 1rem;
    min-height: 100px;
  }

  .file-drop-text {
    font-size: 0.75rem;
  }

  .file-drop-hint {
    font-size: 0.625rem;
  }

  .image-preview-small .image-preview,
  .video-preview-small .video-preview {
    height: 120px;
  }

  .image-preview-medium .image-preview,
  .video-preview-medium .video-preview {
    height: 150px;
  }

  .image-preview-large .image-preview,
  .video-preview-large .video-preview {
    height: 180px;
  }
}

@media (max-width: 480px) {
  .image-preview-grid {
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }

  .file-item {
    padding: 0.5rem 0.75rem;
  }

  .file-meta {
    flex-direction: column;
    gap: 0.25rem;
  }

  .image-modal-overlay {
    padding: 1rem;
  }
}

/* Theme Selector Color Preview Styles */
.theme-color-light-0 { background-color: #3B82F6; }
.theme-color-light-1 { background-color: #10B981; }
.theme-color-light-2 { background-color: #F59E0B; }

.theme-color-professional-0 { background-color: #1F2937; }
.theme-color-professional-1 { background-color: #50BEA7; }
.theme-color-professional-2 { background-color: #DDD; }

.theme-color-creative-0 { background-color: #8B5CF6; }
.theme-color-creative-1 { background-color: #EC4899; }
.theme-color-creative-2 { background-color: #F97316; }

.theme-color-minimal-0 { background-color: #6B7280; }
.theme-color-minimal-1 { background-color: #9CA3AF; }
.theme-color-minimal-2 { background-color: #E5E7EB; }

.theme-color-dark-0 { background-color: #3B82F6; }
.theme-color-dark-1 { background-color: #10B981; }
.theme-color-dark-2 { background-color: #F59E0B; }

.theme-color-high-contrast-0 { background-color: #000000; }
.theme-color-high-contrast-1 { background-color: #FFFFFF; }
.theme-color-high-contrast-2 { background-color: #FFD700; }

/* ========================================
   ONBOARDING COMPONENT STYLES
   ======================================== */

.onboarding-container ::-webkit-scrollbar {
  display: none;
}

.onboarding-container {
  scrollbar-width: none;
}

.onboarding-container {
  -ms-overflow-style: none;
}

.onboarding-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.onboarding-btn-primary {
  background-color: rgb(var(--color-secondary-500));
  color: white;
  transition: all 0.2s ease;
}

.onboarding-btn-primary:hover {
  background-color: rgb(var(--color-secondary-600));
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(var(--color-secondary-500), 0.3);
}

.onboarding-btn-secondary {
  background-color: rgb(var(--color-surface-200));
  color: rgb(var(--color-surface-700));
  transition: all 0.2s ease;
}

.onboarding-btn-secondary:hover {
  background-color: rgb(var(--color-surface-300));
}

.onboarding-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid rgb(var(--color-surface-300));
  border-radius: 0.375rem;
  background-color: rgb(var(--color-surface-50));
  color: rgb(var(--color-surface-900));
  transition: all 0.2s ease;
  outline: none;
}

.onboarding-input:focus {
  border-color: rgb(var(--color-secondary-500));
  box-shadow: 0 0 0 2px rgba(var(--color-secondary-500), 0.2);
}

/* Theme-specific support for onboarding components */

/* Dark Theme */
[data-theme="dark"] .onboarding-btn-secondary {
  background-color: rgb(var(--color-surface-700));
  color: rgb(var(--color-surface-200));
}

[data-theme="dark"] .onboarding-btn-secondary:hover {
  background-color: rgb(var(--color-surface-600));
}

[data-theme="dark"] .onboarding-input {
  border-color: rgb(var(--color-surface-600));
  background-color: rgb(var(--color-surface-800));
  color: rgb(var(--color-surface-100));
}

/* Ocean Theme - Enhanced blue/teal tones */
[data-theme="ocean"] .step-completed {
  background: linear-gradient(135deg, rgb(var(--color-accent-600)), rgb(var(--color-accent-700)));
  box-shadow: 0 10px 15px -3px rgba(var(--color-accent-500), 0.2), 0 4px 6px -2px rgba(var(--color-accent-500), 0.1);
}

[data-theme="ocean"] .step-current {
  background: linear-gradient(135deg, rgb(var(--color-accent-500)), rgb(var(--color-accent-600)));
  box-shadow: 0 8px 12px -2px rgba(var(--color-accent-500), 0.25), 0 4px 6px -1px rgba(var(--color-accent-500), 0.15);
}

[data-theme="ocean"] .step-border-completed {
  border-color: rgb(var(--color-accent-600));
}

[data-theme="ocean"] .step-border-current {
  border-color: rgb(var(--color-accent-500));
}

[data-theme="ocean"] .step-text-completed {
  color: rgb(var(--color-accent-700));
}

[data-theme="ocean"] .step-dot-completed {
  background-color: rgb(var(--color-accent-600));
}

/* Forest Theme - Enhanced green/nature tones */
[data-theme="forest"] .step-completed {
  background: linear-gradient(135deg, rgb(var(--color-primary-600)), rgb(var(--color-primary-700)));
  box-shadow: 0 10px 15px -3px rgba(var(--color-primary-500), 0.2), 0 4px 6px -2px rgba(var(--color-primary-500), 0.1);
}

[data-theme="forest"] .step-current {
  background: linear-gradient(135deg, rgb(var(--color-primary-500)), rgb(var(--color-primary-600)));
  box-shadow: 0 8px 12px -2px rgba(var(--color-primary-500), 0.25), 0 4px 6px -1px rgba(var(--color-primary-500), 0.15);
}

[data-theme="forest"] .step-border-completed {
  border-color: rgb(var(--color-primary-600));
}

[data-theme="forest"] .step-border-current {
  border-color: rgb(var(--color-primary-500));
}

[data-theme="forest"] .step-text-completed {
  color: rgb(var(--color-primary-700));
}

[data-theme="forest"] .step-dot-completed {
  background-color: rgb(var(--color-primary-600));
}

/* Sunset Theme - Enhanced warm orange/red tones */
[data-theme="sunset"] .step-completed {
  background: linear-gradient(135deg, rgb(var(--color-primary-600)), rgb(var(--color-primary-700)));
  box-shadow: 0 10px 15px -3px rgba(var(--color-primary-500), 0.2), 0 4px 6px -2px rgba(var(--color-primary-500), 0.1);
}

[data-theme="sunset"] .step-current {
  background: linear-gradient(135deg, rgb(var(--color-primary-500)), rgb(var(--color-primary-600)));
  box-shadow: 0 8px 12px -2px rgba(var(--color-primary-500), 0.25), 0 4px 6px -1px rgba(var(--color-primary-500), 0.15);
}

[data-theme="sunset"] .step-border-completed {
  border-color: rgb(var(--color-primary-600));
}

[data-theme="sunset"] .step-border-current {
  border-color: rgb(var(--color-primary-500));
}

[data-theme="sunset"] .step-text-completed {
  color: rgb(var(--color-primary-700));
}

[data-theme="sunset"] .step-dot-completed {
  background-color: rgb(var(--color-primary-600));
}

/* Onboarding Step Enhancements - Integrated with admin theme */
.step-completed {
  background: linear-gradient(135deg, rgb(var(--color-secondary-600)), rgb(var(--color-secondary-700)));
  box-shadow: 0 10px 15px -3px rgba(var(--color-secondary-500), 0.2), 0 4px 6px -2px rgba(var(--color-secondary-500), 0.1);
}

.step-current {
  background: linear-gradient(135deg, rgb(var(--color-secondary-500)), rgb(var(--color-secondary-600)));
  box-shadow: 0 8px 12px -2px rgba(var(--color-secondary-500), 0.25), 0 4px 6px -1px rgba(var(--color-secondary-500), 0.15);
  animation: pulse-admin-theme 2s infinite;
}

@keyframes pulse-admin-theme {
  0%, 100% {
    box-shadow: 0 8px 12px -2px rgba(var(--color-secondary-500), 0.25), 0 4px 6px -1px rgba(var(--color-secondary-500), 0.15), 0 0 0 0 rgba(var(--color-secondary-500), 0.7);
  }
  50% {
    box-shadow: 0 8px 12px -2px rgba(var(--color-secondary-500), 0.25), 0 4px 6px -1px rgba(var(--color-secondary-500), 0.15), 0 0 0 8px rgba(var(--color-secondary-500), 0);
  }
}

/* Onboarding card styles that match admin design */
.onboarding-card {
  background-color: rgb(var(--color-card));
  color: rgb(var(--color-card-foreground));
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid rgb(var(--color-border));
}

/* Onboarding table styles */
.onboarding-table-container {
  width: 100%;
  overflow-x: auto;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border-radius: 0.5rem;
  background-color: rgb(var(--color-card));
}

.onboarding-data-table {
  min-width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-color: rgb(var(--color-border));
}

.onboarding-data-table-header {
  background-color: rgb(var(--color-muted));
  color: rgb(var(--color-muted-foreground));
  text-align: left;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.onboarding-data-table-cell {
  padding: 1rem 1.5rem;
  white-space: nowrap;
  font-size: 0.875rem;
  color: rgb(var(--color-foreground));
}

.onboarding-data-table-row {
  transition: background-color 0.2s ease;
}

.onboarding-data-table-row:hover {
  background-color: rgb(var(--color-muted));
}

.onboarding-data-table-row:nth-child(even) {
  background-color: rgba(var(--color-muted), 0.5);
}

/* Primary color utilities for onboarding (maps to admin secondary theme) */
.bg-primary-50 { background-color: rgb(var(--color-secondary-50)); }
.bg-primary-100 { background-color: rgb(var(--color-secondary-100)); }
.bg-primary-500 { background-color: rgb(var(--color-secondary-500)); }
.bg-primary-600 { background-color: rgb(var(--color-secondary-600)); }
.bg-primary-700 { background-color: rgb(var(--color-secondary-700)); }

.text-primary-500 { color: rgb(var(--color-secondary-500)); }
.text-primary-600 { color: rgb(var(--color-secondary-600)); }
.text-primary-700 { color: rgb(var(--color-secondary-700)); }

.border-primary-500 { border-color: rgb(var(--color-secondary-500)); }
.border-primary-600 { border-color: rgb(var(--color-secondary-600)); }

/* Ring utilities for onboarding focus states */
.ring-primary-500 { --tw-ring-color: rgb(var(--color-secondary-500)); }

/* Onboarding loading states */
.onboarding-loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.onboarding-spinner {
  animation: spin 1s linear infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Onboarding success/error states */
.onboarding-success {
  background-color: rgb(var(--color-accent-50));
  border-color: rgb(var(--color-accent-200));
  color: rgb(var(--color-accent-700));
}

.onboarding-error {
  background-color: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

[data-theme="dark"] .onboarding-success {
  background-color: rgb(var(--color-accent-900));
  border-color: rgb(var(--color-accent-700));
  color: rgb(var(--color-accent-300));
}

[data-theme="dark"] .onboarding-error {
  background-color: #7f1d1d;
  border-color: #dc2626;
  color: #fca5a5;
}

/* Step indicator specific styles */
.step-border-completed {
  border-color: rgb(var(--color-secondary-600));
}

.step-border-current {
  border-color: rgb(var(--color-secondary-500));
}

.step-text-completed {
  color: rgb(var(--color-secondary-700));
}

.step-dot-completed {
  background-color: rgb(var(--color-secondary-600));
}

/* Onboarding-specific utility classes */
.text-xxs {
  font-size: 0.625rem;
  line-height: 0.75rem;
}

/* ========================================
   REACT-TOASTIFY THEME INTEGRATION
   ======================================== */

/* Override react-toastify styles to match admin theme */
.Toastify__toast-container {
  font-family: 'Lexend Deca', 'DM Sans', 'IBM Plex Sans', 'Montserrat', 'Clear Sans', Arial, sans-serif;
}

.Toastify__toast {
  background-color: rgb(var(--color-card));
  color: rgb(var(--color-card-foreground));
  border: 1px solid rgb(var(--color-border));
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(var(--color-surface-900), 0.1), 0 4px 6px -2px rgba(var(--color-surface-900), 0.05);
}

.Toastify__toast--success {
  background-color: rgb(var(--color-accent-50));
  border-color: rgb(var(--color-accent-200));
  color: rgb(var(--color-accent-800));
}

.Toastify__toast--error {
  background-color: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

.Toastify__toast--warning {
  background-color: #fffbeb;
  border-color: #fed7aa;
  color: #d97706;
}

.Toastify__toast--info {
  background-color: rgb(var(--color-primary-50));
  border-color: rgb(var(--color-primary-200));
  color: rgb(var(--color-primary-800));
}

[data-theme="dark"] .Toastify__toast {
  background-color: rgb(var(--color-surface-800));
  color: rgb(var(--color-surface-100));
  border-color: rgb(var(--color-surface-600));
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .Toastify__toast--success {
  background-color: rgb(var(--color-accent-900));
  border-color: rgb(var(--color-accent-700));
  color: rgb(var(--color-accent-200));
}

[data-theme="dark"] .Toastify__toast--error {
  background-color: #7f1d1d;
  border-color: #dc2626;
  color: #fca5a5;
}

[data-theme="dark"] .Toastify__toast--warning {
  background-color: #78350f;
  border-color: #d97706;
  color: #fed7aa;
}

[data-theme="dark"] .Toastify__toast--info {
  background-color: rgb(var(--color-primary-900));
  border-color: rgb(var(--color-primary-700));
  color: rgb(var(--color-primary-200));
}

.Toastify__progress-bar {
  background: rgb(var(--color-secondary-500));
}

.Toastify__close-button {
  color: rgb(var(--color-muted-foreground));
}

.Toastify__close-button:hover {
  color: rgb(var(--color-foreground));
}

/* ========================================
   THEME-SPECIFIC TOAST ENHANCEMENTS
   ======================================== */

/* Ocean Theme - Enhanced toast notifications */
[data-theme="ocean"] .Toastify__toast--success {
  background-color: rgb(var(--color-accent-50));
  border-color: rgb(var(--color-accent-200));
  color: rgb(var(--color-accent-800));
}

[data-theme="ocean"] .Toastify__toast--info {
  background-color: rgb(var(--color-primary-50));
  border-color: rgb(var(--color-primary-200));
  color: rgb(var(--color-primary-800));
}

[data-theme="ocean"] .Toastify__progress-bar {
  background: rgb(var(--color-accent-500));
}

/* Forest Theme - Enhanced toast notifications */
[data-theme="forest"] .Toastify__toast--success {
  background-color: rgb(var(--color-primary-50));
  border-color: rgb(var(--color-primary-200));
  color: rgb(var(--color-primary-800));
}

[data-theme="forest"] .Toastify__toast--info {
  background-color: rgb(var(--color-accent-50));
  border-color: rgb(var(--color-accent-200));
  color: rgb(var(--color-accent-800));
}

[data-theme="forest"] .Toastify__progress-bar {
  background: rgb(var(--color-primary-500));
}

/* Sunset Theme - Enhanced toast notifications */
[data-theme="sunset"] .Toastify__toast--success {
  background-color: rgb(var(--color-accent-50));
  border-color: rgb(var(--color-accent-200));
  color: rgb(var(--color-accent-800));
}

[data-theme="sunset"] .Toastify__toast--info {
  background-color: rgb(var(--color-primary-50));
  border-color: rgb(var(--color-primary-200));
  color: rgb(var(--color-primary-800));
}

[data-theme="sunset"] .Toastify__progress-bar {
  background: rgb(var(--color-primary-500));
}

/* Professional Theme - Enhanced toast notifications */
[data-theme="professional"] .Toastify__progress-bar {
  background: rgb(var(--color-secondary-500));
}

