using Domain.Entities;
using Abstraction.Database.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.ObjectLookup.Commands.UpsertObjectLookup;

/// <summary>
/// Handler for UpsertObjectLookupCommand
/// </summary>
public class UpsertObjectLookupCommandHandler : IRequestHandler<UpsertObjectLookupCommand, Result<Guid>>
{
    private readonly IRepository<Domain.Entities.ObjectLookup> _objectLookupRepository;
    private readonly IRepository<Domain.Entities.Object> _objectRepository;
    private readonly ILogger<UpsertObjectLookupCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpsertObjectLookupCommandHandler(
        IRepository<Domain.Entities.ObjectLookup> objectLookupRepository,
        IRepository<Domain.Entities.Object> objectRepository,
        ILogger<UpsertObjectLookupCommandHandler> logger)
    {
        _objectLookupRepository = objectLookupRepository;
        _objectRepository = objectRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<Guid>> Handle(UpsertObjectLookupCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Validate ObjectId if provided
            if (request.ObjectId.HasValue)
            {
                var objectExists = await _objectRepository.GetByIdAsync(request.ObjectId.Value, cancellationToken);
                if (objectExists == null)
                {
                    return Result<Guid>.Failure($"Object with ID '{request.ObjectId}' not found.");
                }
            }

            Domain.Entities.ObjectLookup objectLookup;

            if (request.Id.HasValue)
            {
                // Update existing ObjectLookup
                objectLookup = await _objectLookupRepository.GetByIdAsync(request.Id.Value, cancellationToken);
                if (objectLookup == null)
                {
                    return Result<Guid>.Failure($"ObjectLookup with ID '{request.Id}' not found.");
                }

                // Update properties
                objectLookup.Name = request.Name;
                objectLookup.SourceType = request.SourceType;
                objectLookup.ObjectId = request.ObjectId;
                objectLookup.DisplayField = request.DisplayField;
                objectLookup.ValueField = request.ValueField;
                objectLookup.MetadataFieldForDisplay = request.MetadataFieldForDisplay;
                objectLookup.MetadataFieldForValue = request.MetadataFieldForValue;
                objectLookup.SupportsTenantFiltering = request.SupportsTenantFiltering;
                objectLookup.SortBy = request.SortBy;
                objectLookup.SortOrder = request.SortOrder;
                objectLookup.IsActive = request.IsActive;
                objectLookup.ModifiedAt = DateTime.UtcNow;

                await _objectLookupRepository.UpdateAsync(objectLookup, cancellationToken);
                _logger.LogInformation("Updated ObjectLookup with ID: {ObjectLookupId}", objectLookup.Id);
            }
            else
            {
                // Create new ObjectLookup
                objectLookup = new Domain.Entities.ObjectLookup
                {
                    Name = request.Name,
                    SourceType = request.SourceType,
                    ObjectId = request.ObjectId,
                    DisplayField = request.DisplayField,
                    ValueField = request.ValueField,
                    MetadataFieldForDisplay = request.MetadataFieldForDisplay,
                    MetadataFieldForValue = request.MetadataFieldForValue,
                    SupportsTenantFiltering = request.SupportsTenantFiltering,
                    SortBy = request.SortBy,
                    SortOrder = request.SortOrder,
                    IsActive = request.IsActive,
                    CreatedAt = DateTime.UtcNow,
                    ModifiedAt = DateTime.UtcNow
                };

                await _objectLookupRepository.AddAsync(objectLookup, cancellationToken);
                _logger.LogInformation("Created new ObjectLookup with ID: {ObjectLookupId}", objectLookup.Id);
            }

            return Result<Guid>.Success(objectLookup.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error upserting ObjectLookup with Name: {Name}", request.Name);
            return Result<Guid>.Failure("An error occurred while upserting the ObjectLookup.");
        }
    }
}
