using Ardalis.Specification;
using Domain.Entities;

namespace Application.Subscriptions.Specifications;

/// <summary>
/// Specification for getting subscriptions with filters and pagination
/// </summary>
public class SubscriptionsWithFiltersSpec : Specification<Subscription>
{
    public SubscriptionsWithFiltersSpec(
        string? searchTerm = null,
        Guid? productId = null,
        string? status = null,
        bool? isActive = null,
        string? orderBy = null,
        int skip = 0,
        int take = 10)
    {
        Query.Where(s => !s.IsDeleted);

        if (productId.HasValue)
        {
            Query.Where(s => s.ProductId == productId.Value);
        }

        if (!string.IsNullOrEmpty(status))
        {
            Query.Where(s => s.Status == status);
        }

        if (isActive.HasValue)
        {
            Query.Where(s => s.IsActive == isActive.Value);
        }

        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(s => 
                s.SubscriptionType.Contains(searchTerm) ||
                s.Product.Name.Contains(searchTerm) ||
                (s.PricingTier != null && s.PricingTier.Contains(searchTerm)));
        }

        // Apply ordering
        if (!string.IsNullOrEmpty(orderBy))
        {
            switch (orderBy.ToLower())
            {
                case "subscriptiontype":
                    Query.OrderBy(s => s.SubscriptionType);
                    break;
                case "status":
                    Query.OrderBy(s => s.Status);
                    break;
                case "startdate":
                    Query.OrderBy(s => s.StartDate);
                    break;
                case "enddate":
                    Query.OrderBy(s => s.EndDate);
                    break;
                case "productname":
                    Query.OrderBy(s => s.Product.Name);
                    break;
                default:
                    Query.OrderByDescending(s => s.CreatedAt);
                    break;
            }
        }
        else
        {
            Query.OrderByDescending(s => s.CreatedAt);
        }

        Query.Include(s => s.Product)
             .Include(s => s.SubscriptionMetadata)
             .Skip(skip)
             .Take(take);
    }
}
