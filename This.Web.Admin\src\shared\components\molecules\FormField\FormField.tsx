import React from 'react';
import { Input } from '@/shared/components/atoms/Input/Input';
import { Checkbox } from '@/shared/components/atoms/Checkbox/Checkbox';
import { Textarea } from '@/shared/components/atoms/Textarea/Textarea';
import { Select } from '@/shared/components/atoms/Select/Select';
import { Label } from '@/shared/components/atoms/Label/Label';

interface FormFieldProps {
  field: {
    name: string;
    label?: string;
    displayLabel?: string;
    type?: string;
    inputType?: string;
    uiComponent?: string;
    placeholder?: string;
    helpText?: string | null;
    required?: boolean | null;
    isRequired?: boolean | null;
    isReadonly?: boolean;
    options?: Array<{ value: string; label: string }>;
    defaultOptions?: Array<{ value: string; label: string }>;
    defaultValue?: any;
    min?: number | string;
    max?: number | string;
    minLength?: number | null;
    maxLength?: number | null;
    [key: string]: any;
  };
  value: any;
  onChange: (name: string, value: any) => void;
  error?: string | string[];
}

export const FormField: React.FC<FormFieldProps> = ({ field, value, onChange, error }) => {
  const {
    name,
    label,
    displayLabel,
    type = 'text',
    inputType,
    uiComponent,
    placeholder,
    helpText,
    required = false,
    isRequired = false,
    isReadonly = false,
    options,
    defaultOptions,
    min,
    max,
    minLength,
    maxLength
  } = field;

  const fieldLabel = displayLabel || label || name;
  const isRequiredField = required || isRequired;
  const fieldPlaceholder = placeholder || `Enter ${fieldLabel}`;
  const fieldType = inputType || type;

  // Normalize error to array format
  const errorMessages = error ? (Array.isArray(error) ? error : [error]) : [];
  const hasError = errorMessages.length > 0;

  // Determine the appropriate component based on field type and uiComponent
  const renderFieldInput = () => {
    // Use uiComponent as priority if provided
    if (uiComponent) {
      switch (uiComponent.toLowerCase()) {
        case 'checkbox':
        case 'thischeckbox':
          return (
            <div className="flex items-center h-10">
              <Checkbox
                id={name}
                name={name}
                checked={!!value}
                onChange={(e) => onChange(name, e.target.checked)}
                disabled={isReadonly}
              />
              <label htmlFor={name} className="ml-2 text-sm text-gray-700">
                {fieldLabel}
              </label>
            </div>
          );
        case 'textarea':
        case 'thistextarea':
          return (
            <Textarea
              id={name}
              name={name}
              value={value || ''}
              onChange={(e) => onChange(name, e.target.value)}
              placeholder={fieldPlaceholder}
              disabled={isReadonly}
              className={`w-full ${hasError ? 'border-red-500' : ''}`}
              minLength={minLength || undefined}
              maxLength={maxLength || undefined}
              rows={5}
            />
          );
        case 'select':
        case 'thisselect':
        case 'dropdown':
          const selectOptions = options || defaultOptions || [];
          return (
            <Select
              id={name}
              name={name}
              value={value || ''}
              onChange={(e) => onChange(name, e.target.value)}
              disabled={isReadonly}
              className={`w-full ${hasError ? 'border-red-500' : ''}`}
            >
              <option value="">{placeholder || 'Select...'}</option>
              {selectOptions.map((option: any) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </Select>
          );
        case 'date':
        case 'thisdate':
          return (
            <Input
              type="date"
              id={name}
              name={name}
              value={value || ''}
              onChange={(e) => onChange(name, e.target.value)}
              placeholder={fieldPlaceholder}
              disabled={isReadonly}
              className={`w-full ${hasError ? 'border-red-500' : ''}`}
              min={min?.toString()}
              max={max?.toString()}
            />
          );
        case 'text':
        case 'thistext':
        default:
          return (
            <Input
              type={fieldType || 'text'}
              id={name}
              name={name}
              value={value || ''}
              onChange={(e) => {
                const newValue = fieldType === 'number' 
                  ? e.target.value === '' ? '' : Number(e.target.value)
                  : e.target.value;
                onChange(name, newValue);
              }}
              placeholder={fieldPlaceholder}
              disabled={isReadonly}
              className={`w-full ${hasError ? 'border-red-500' : ''}`}
              min={min?.toString()}
              max={max?.toString()}
              minLength={minLength || undefined}
              maxLength={maxLength || undefined}
              required={isRequiredField || undefined}
            />
          );
      }
    }

    // If no uiComponent, use field type
    switch (fieldType.toLowerCase()) {
      case 'boolean':
        return (
          <div className="flex items-center h-10">
            <Checkbox
              id={name}
              name={name}
              checked={!!value}
              onChange={(e) => onChange(name, e.target.checked)}
              disabled={isReadonly}
            />
            <label htmlFor={name} className="ml-2 text-sm text-gray-700">
              {fieldLabel}
            </label>
          </div>
        );
      case 'textarea':
      case 'longtext':
        return (
          <Textarea
            id={name}
            name={name}
            value={value || ''}
            onChange={(e) => onChange(name, e.target.value)}
            placeholder={fieldPlaceholder}
            disabled={isReadonly}
            className={`w-full ${hasError ? 'border-red-500' : ''}`}
            minLength={minLength || undefined}
            maxLength={maxLength || undefined}
            rows={5}
          />
        );
      case 'select':
      case 'enum':
      case 'dropdown':
        const selectOptions = options || defaultOptions || [];
        return (
          <Select
            id={name}
            name={name}
            value={value || ''}
            onChange={(e) => onChange(name, e.target.value)}
            disabled={isReadonly}
            className={`w-full ${hasError ? 'border-red-500' : ''}`}
          >
            <option value="">{placeholder || 'Select...'}</option>
            {selectOptions.map((option: any) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </Select>
        );
      case 'date':
        return (
          <Input
            type="date"
            id={name}
            name={name}
            value={value || ''}
            onChange={(e) => onChange(name, e.target.value)}
            placeholder={fieldPlaceholder}
            disabled={isReadonly}
            className={`w-full ${hasError ? 'border-red-500' : ''}`}
            min={min?.toString()}
            max={max?.toString()}
          />
        );
      case 'datetime':
        return (
          <Input
            type="datetime-local"
            id={name}
            name={name}
            value={value || ''}
            onChange={(e) => onChange(name, e.target.value)}
            placeholder={fieldPlaceholder}
            disabled={isReadonly}
            className={`w-full ${hasError ? 'border-red-500' : ''}`}
            min={min?.toString()}
            max={max?.toString()}
          />
        );
      case 'number':
      case 'decimal':
      case 'float':
      case 'integer':
        return (
          <Input
            type="number"
            id={name}
            name={name}
            value={value || ''}
            onChange={(e) => {
              const newValue = e.target.value === '' ? '' : Number(e.target.value);
              onChange(name, newValue);
            }}
            placeholder={fieldPlaceholder}
            disabled={isReadonly}
            className={`w-full ${hasError ? 'border-red-500' : ''}`}
            min={min?.toString()}
            max={max?.toString()}
            step={fieldType === 'integer' ? '1' : 'any'}
            required={isRequiredField || undefined}
          />
        );
      case 'text':
      case 'string':
      default:
        return (
          <Input
            type="text"
            id={name}
            name={name}
            value={value || ''}
            onChange={(e) => onChange(name, e.target.value)}
            placeholder={fieldPlaceholder}
            disabled={isReadonly}
            className={`w-full ${hasError ? 'border-red-500' : ''}`}
            minLength={minLength || undefined}
            maxLength={maxLength || undefined}
            required={isRequiredField || undefined}
          />
        );
    }
  };

  // For checkbox type, we render differently (label is next to the checkbox)
  if ((uiComponent?.toLowerCase() === 'checkbox' || uiComponent?.toLowerCase() === 'thischeckbox' || 
       fieldType.toLowerCase() === 'boolean') && !isReadonly) {
    return (
      <div className="mb-4">
        {renderFieldInput()}
        {helpText && <p className="mt-1 text-sm text-gray-500">{helpText}</p>}
        {hasError && (
          <div className="mt-1 text-sm text-red-600">
            {errorMessages.map((error, idx) => (
              <p key={`${name}-error-${idx}`}>{error}</p>
            ))}
          </div>
        )}
      </div>
    );
  }

  // Standard layout for other field types
  return (
    <div className="mb-4">
      <Label htmlFor={name} className="mb-1 block">
        {fieldLabel}
        {isRequiredField && <span className="text-red-500 ml-1">*</span>}
      </Label>
      {renderFieldInput()}
      {helpText && <p className="mt-1 text-sm text-gray-500">{helpText}</p>}
      {hasError && (
        <div className="mt-1 text-sm text-red-600">
          {errorMessages.map((error, idx) => (
            <p key={`${name}-error-${idx}`}>{error}</p>
          ))}
        </div>
      )}
    </div>
  );
};
