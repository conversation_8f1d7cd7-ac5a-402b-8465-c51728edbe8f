import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Form, <PERSON>, Bad<PERSON>, <PERSON><PERSON>, Spin<PERSON>, <PERSON>, Col } from 'react-bootstrap';
import { Plus, Edit, Trash2, Save, X, Star, <PERSON>Off, ArrowUp, ArrowDown, Search } from 'lucide-react';
import { contextService } from '../../services/contextService';
import type { ContextWithLookups, Lookup } from '../../types/context';

interface LookupManagementModalProps {
  show: boolean;
  onHide: () => void;
  contextWithLookups: ContextWithLookups | null;
  onSaved: () => void;
}

interface LookupFormData {
  id?: string;
  value: string;
  isDefault: boolean;
  value1: string;
  value2: string;
  showSequence: number;
  isActive: boolean;
}

export const LookupManagementModal: React.FC<LookupManagementModalProps> = ({
  show,
  onHide,
  contextWithLookups,
  onSaved
}) => {
  const [lookups, setLookups] = useState<Lookup[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingLookup, setEditingLookup] = useState<Lookup | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [formData, setFormData] = useState<LookupFormData>({
    value: '',
    isDefault: false,
    value1: '',
    value2: '',
    showSequence: 1,
    isActive: true
  });

  useEffect(() => {
    if (show && contextWithLookups) {
      setLookups([...contextWithLookups.lookups].sort((a, b) => a.showSequence - b.showSequence));
      setError(null);
      setShowAddForm(false);
      setEditingLookup(null);
      setSearchTerm('');
    }
  }, [show, contextWithLookups]);

  const resetForm = () => {
    setFormData({
      value: '',
      isDefault: false,
      value1: '',
      value2: '',
      showSequence: Math.max(...lookups.map(l => l.showSequence), 0) + 1,
      isActive: true
    });
  };

  const handleAddNew = () => {
    resetForm();
    setEditingLookup(null);
    setShowAddForm(true);
  };

  const handleEdit = (lookup: Lookup) => {
    setFormData({
      id: lookup.id,
      value: lookup.value,
      isDefault: lookup.isDefault,
      value1: lookup.value1 || '',
      value2: lookup.value2 || '',
      showSequence: lookup.showSequence,
      isActive: lookup.isActive
    });
    setEditingLookup(lookup);
    setShowAddForm(true);
  };

  const handleDelete = async (lookupId: string) => {
    if (!window.confirm('Are you sure you want to delete this lookup?')) {
      return;
    }

    try {
      setLoading(true);
      await contextService.deleteLookup(lookupId);
      setLookups(prev => prev.filter(l => l.id !== lookupId));
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete lookup');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitForm = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.value.trim()) {
      setError('Value is required');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      if (editingLookup) {
        // Update existing lookup
        await contextService.updateLookup({
          id: editingLookup.id,
          contextId: contextWithLookups!.context.id,
          value: formData.value.trim(),
          isDefault: formData.isDefault,
          value1: formData.value1.trim() || undefined,
          value2: formData.value2.trim() || undefined,
          showSequence: formData.showSequence,
          isActive: formData.isActive
        });

        // Update local state
        setLookups(prev => prev.map(l => 
          l.id === editingLookup.id 
            ? { ...l, ...formData, value: formData.value.trim() }
            : { ...l, isDefault: formData.isDefault ? false : l.isDefault }
        ));
      } else {
        // Create new lookup
        const result = await contextService.createLookup({
          contextId: contextWithLookups!.context.id,
          value: formData.value.trim(),
          isDefault: formData.isDefault,
          value1: formData.value1.trim() || undefined,
          value2: formData.value2.trim() || undefined,
          showSequence: formData.showSequence,
          isActive: formData.isActive
        });

        // Add to local state
        const newLookup: Lookup = {
          id: result.id,
          contextId: contextWithLookups!.context.id,
          value: formData.value.trim(),
          isDefault: formData.isDefault,
          value1: formData.value1.trim() || undefined,
          value2: formData.value2.trim() || undefined,
          showSequence: formData.showSequence,
          isActive: formData.isActive,
          isDeleted: false,
          createdAt: new Date().toISOString()
        };

        setLookups(prev => {
          const updated = formData.isDefault 
            ? prev.map(l => ({ ...l, isDefault: false }))
            : prev;
          return [...updated, newLookup].sort((a, b) => a.showSequence - b.showSequence);
        });
      }

      setShowAddForm(false);
      setEditingLookup(null);
      resetForm();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to save lookup');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleDefault = async (lookup: Lookup) => {
    try {
      setLoading(true);
      await contextService.updateLookup({
        id: lookup.id,
        contextId: lookup.contextId,
        value: lookup.value,
        isDefault: !lookup.isDefault,
        value1: lookup.value1,
        value2: lookup.value2,
        showSequence: lookup.showSequence,
        isActive: lookup.isActive
      });

      setLookups(prev => prev.map(l => ({
        ...l,
        isDefault: l.id === lookup.id ? !lookup.isDefault : false
      })));
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update default status');
    } finally {
      setLoading(false);
    }
  };

  const moveSequence = async (lookup: Lookup, direction: 'up' | 'down') => {
    const sortedLookups = [...lookups].sort((a, b) => a.showSequence - b.showSequence);
    const currentIndex = sortedLookups.findIndex(l => l.id === lookup.id);
    
    if (
      (direction === 'up' && currentIndex === 0) ||
      (direction === 'down' && currentIndex === sortedLookups.length - 1)
    ) {
      return;
    }

    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    const targetLookup = sortedLookups[targetIndex];

    try {
      setLoading(true);
      
      // Swap sequences
      await contextService.updateLookup({
        id: lookup.id,
        contextId: lookup.contextId,
        value: lookup.value,
        isDefault: lookup.isDefault,
        value1: lookup.value1,
        value2: lookup.value2,
        showSequence: targetLookup.showSequence,
        isActive: lookup.isActive
      });

      await contextService.updateLookup({
        id: targetLookup.id,
        contextId: targetLookup.contextId,
        value: targetLookup.value,
        isDefault: targetLookup.isDefault,
        value1: targetLookup.value1,
        value2: targetLookup.value2,
        showSequence: lookup.showSequence,
        isActive: targetLookup.isActive
      });

      setLookups(prev => prev.map(l => {
        if (l.id === lookup.id) return { ...l, showSequence: targetLookup.showSequence };
        if (l.id === targetLookup.id) return { ...l, showSequence: lookup.showSequence };
        return l;
      }));
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to reorder lookups');
    } finally {
      setLoading(false);
    }
  };

  // Filter lookups based on search term
  const filteredLookups = lookups.filter(lookup => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    return (
      lookup.value.toLowerCase().includes(searchLower) ||
      (lookup.value1 && lookup.value1.toLowerCase().includes(searchLower)) ||
      (lookup.value2 && lookup.value2.toLowerCase().includes(searchLower))
    );
  });

  if (!contextWithLookups) return null;

  return (
    <Modal show={show} onHide={onHide} size="xl">
      <Modal.Header closeButton>
        <Modal.Title>
          Manage Lookups - {contextWithLookups.context.name}
        </Modal.Title>
      </Modal.Header>

      <Modal.Body>
        {error && (
          <Alert variant="danger" dismissible onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        <div className="d-flex justify-content-between align-items-center mb-3">
          <h6 className="mb-0">Lookup Values ({filteredLookups.length} of {lookups.length})</h6>
          <Button variant="primary" size="sm" onClick={handleAddNew} disabled={loading}>
            <Plus size={16} className="me-2" />
            Add Lookup
          </Button>
        </div>

        {/* Search Filter */}
        <div className="mb-3">
          <Form.Group>
            <Form.Label>Search Lookups</Form.Label>
            <div className="position-relative">
              <Form.Control
                type="text"
                placeholder="Search by value, value1, or value2..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                disabled={loading}
              />
              <Search size={16} className="position-absolute top-50 end-0 translate-middle-y me-3 text-muted" />
            </div>
            <Form.Text className="text-muted">
              Filter lookup values by searching across Value, Value1, and Value2 fields
            </Form.Text>
          </Form.Group>
        </div>

        {/* Add/Edit Form */}
        {showAddForm && (
          <Form onSubmit={handleSubmitForm} className="mb-4 p-3 border rounded bg-light">
            <h6>{editingLookup ? 'Edit Lookup' : 'Add New Lookup'}</h6>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Value <span className="text-danger">*</span></Form.Label>
                  <Form.Control
                    type="text"
                    value={formData.value}
                    onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
                    placeholder="Enter lookup value"
                    required
                    disabled={loading}
                  />
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Sequence</Form.Label>
                  <Form.Control
                    type="number"
                    min="1"
                    value={formData.showSequence}
                    onChange={(e) => setFormData(prev => ({ ...prev, showSequence: parseInt(e.target.value) || 1 }))}
                    disabled={loading}
                  />
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>&nbsp;</Form.Label>
                  <div>
                    <Form.Check
                      type="switch"
                      id="lookup-default"
                      label="Default"
                      checked={formData.isDefault}
                      onChange={(e) => setFormData(prev => ({ ...prev, isDefault: e.target.checked }))}
                      disabled={loading}
                    />
                    <Form.Check
                      type="switch"
                      id="lookup-active"
                      label="Active"
                      checked={formData.isActive}
                      onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                      disabled={loading}
                    />
                  </div>
                </Form.Group>
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Value 1 (Optional)</Form.Label>
                  <Form.Control
                    type="text"
                    value={formData.value1}
                    onChange={(e) => setFormData(prev => ({ ...prev, value1: e.target.value }))}
                    placeholder="Additional value 1"
                    disabled={loading}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Value 2 (Optional)</Form.Label>
                  <Form.Control
                    type="text"
                    value={formData.value2}
                    onChange={(e) => setFormData(prev => ({ ...prev, value2: e.target.value }))}
                    placeholder="Additional value 2"
                    disabled={loading}
                  />
                </Form.Group>
              </Col>
            </Row>
            <div className="d-flex gap-2">
              <Button variant="primary" type="submit" disabled={loading}>
                {loading ? <Spinner animation="border" size="sm" className="me-2" /> : <Save size={16} className="me-2" />}
                {editingLookup ? 'Update' : 'Add'} Lookup
              </Button>
              <Button 
                variant="secondary" 
                onClick={() => {
                  setShowAddForm(false);
                  setEditingLookup(null);
                  resetForm();
                }}
                disabled={loading}
              >
                <X size={16} className="me-2" />
                Cancel
              </Button>
            </div>
          </Form>
        )}

        {/* Lookups Table */}
        <Table responsive hover>
          <thead className="table-light">
            <tr>
              <th>Sequence</th>
              <th>Value</th>
              <th>Value 1</th>
              <th>Value 2</th>
              <th>Default</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredLookups.length === 0 ? (
              <tr>
                <td colSpan={7} className="text-center text-muted p-4">
                  {searchTerm ? 'No lookups match your search criteria.' : 'No lookups found. Click "Add Lookup" to create one.'}
                </td>
              </tr>
            ) : (
              filteredLookups
                .sort((a, b) => a.showSequence - b.showSequence)
                .map((lookup, index) => (
                  <tr key={lookup.id}>
                    <td>
                      <div className="d-flex align-items-center gap-1">
                        <span>{lookup.showSequence}</span>
                        <div className="d-flex flex-column">
                          <Button
                            variant="link"
                            size="sm"
                            className="p-0 lh-1"
                            onClick={() => moveSequence(lookup, 'up')}
                            disabled={lookups.findIndex(l => l.id === lookup.id) === 0 || loading}
                            title="Move up"
                          >
                            <ArrowUp size={12} />
                          </Button>
                          <Button
                            variant="link"
                            size="sm"
                            className="p-0 lh-1"
                            onClick={() => moveSequence(lookup, 'down')}
                            disabled={lookups.findIndex(l => l.id === lookup.id) === lookups.length - 1 || loading}
                            title="Move down"
                          >
                            <ArrowDown size={12} />
                          </Button>
                        </div>
                      </div>
                    </td>
                    <td><strong>{lookup.value}</strong></td>
                    <td>{lookup.value1 || '-'}</td>
                    <td>{lookup.value2 || '-'}</td>
                    <td>
                      <Button
                        variant="link"
                        size="sm"
                        className="p-0"
                        onClick={() => handleToggleDefault(lookup)}
                        disabled={loading}
                        title={lookup.isDefault ? "Remove as default" : "Set as default"}
                      >
                        {lookup.isDefault ? (
                          <Star size={16} className="text-warning" fill="currentColor" />
                        ) : (
                          <StarOff size={16} className="text-muted" />
                        )}
                      </Button>
                    </td>
                    <td>
                      <Badge bg={lookup.isActive ? 'success' : 'warning'}>
                        {lookup.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </td>
                    <td>
                      <div className="d-flex gap-1">
                        <Button
                          variant="outline-primary"
                          size="sm"
                          onClick={() => handleEdit(lookup)}
                          disabled={loading}
                          title="Edit lookup"
                        >
                          <Edit size={12} />
                        </Button>
                        <Button
                          variant="outline-danger"
                          size="sm"
                          onClick={() => handleDelete(lookup.id)}
                          disabled={loading}
                          title="Delete lookup"
                        >
                          <Trash2 size={12} />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
            )}
          </tbody>
        </Table>
      </Modal.Body>

      <Modal.Footer>
        <Button variant="secondary" onClick={onHide} disabled={loading}>
          Close
        </Button>
        <Button variant="primary" onClick={onSaved} disabled={loading}>
          Done
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
