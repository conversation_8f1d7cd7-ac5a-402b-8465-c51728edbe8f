using Application.Objects.DTOs;
using Application.Objects.Specifications;
using Abstraction.Database.Repositories;
using MediatR;
using Shared.Common.Response;
using ObjectEntity = Domain.Entities.Object;

namespace Application.Objects.Queries;

/// <summary>
/// Get Objects query handler
/// </summary>
public class GetObjectsQueryHandler : IRequestHandler<GetObjectsQuery, PaginatedResult<ObjectDto>>
{
    private readonly IReadRepository<ObjectEntity> _objectRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetObjectsQueryHandler(IReadRepository<ObjectEntity> objectRepository)
    {
        _objectRepository = objectRepository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<PaginatedResult<ObjectDto>> Handle(GetObjectsQuery request, CancellationToken cancellationToken)
    {
        var skip = (request.PageNumber - 1) * request.PageSize;

        // Create specifications (removed FeatureId references)
        var dataSpec = new ObjectWithMetadataSpec(
            searchTerm: request.SearchTerm,
            featureId: null, // Feature references removed
            isActive: request.IsActive,
            orderBy: request.OrderBy,
            skip: skip,
            take: request.PageSize);

        var countSpec = new ObjectCountSpec(
            searchTerm: request.SearchTerm,
            featureId: null, // Feature references removed
            isActive: request.IsActive);

        // Get data and count using specifications (tenant isolation handled by Finbuckle.MultiTenant)
        var objects = await _objectRepository.ListAsync(dataSpec, cancellationToken);
        var totalCount = await _objectRepository.CountAsync(countSpec, cancellationToken);

        var objectDtos = objects.Select(obj => new ObjectDto
        {
            Id = obj.Id,
            ProductId = obj.ProductId, // Use ProductId instead of FeatureId
            ProductName = obj.Product?.Name, // Use Product instead of Feature
            ParentObjectId = obj.ParentObjectId,
            ParentObjectName = obj.ParentObject?.Name,
            Name = obj.Name,
            Description = obj.Description,
            Icon = obj.Icon,
            IsActive = obj.IsActive,
            ChildObjectsCount = obj.ChildObjects?.Count(co => co.IsActive && !co.IsDeleted) ?? 0,
            MetadataCount = obj.ObjectMetadata?.Count(om => om.IsActive && !om.IsDeleted) ?? 0,
            CreatedAt = obj.CreatedAt,
            CreatedBy = obj.CreatedBy ?? Guid.Empty,
            ModifiedAt = obj.ModifiedAt,
            ModifiedBy = obj.ModifiedBy ?? Guid.Empty
        }).ToList();

        return new PaginatedResult<ObjectDto>(objectDtos, request.PageNumber, request.PageSize, totalCount);
    }
}
