using MediatR;
using Shared.Common.Response;

namespace Application.Context.Commands.UpsertLookup;

/// <summary>
/// Command to upsert a single lookup
/// </summary>
public class UpsertLookupCommand : IRequest<Result<Guid>>
{
    /// <summary>
    /// Lookup ID (optional for insert)
    /// </summary>
    public Guid? Id { get; set; }

    /// <summary>
    /// Context ID
    /// </summary>
    public Guid ContextId { get; set; }

    /// <summary>
    /// Lookup value
    /// </summary>
    public string Value { get; set; } = string.Empty;

    /// <summary>
    /// Whether this is the default value
    /// </summary>
    public bool IsDefault { get; set; }

    /// <summary>
    /// Additional value 1
    /// </summary>
    public string? Value1 { get; set; }

    /// <summary>
    /// Additional value 2
    /// </summary>
    public string? Value2 { get; set; }

    /// <summary>
    /// Display sequence
    /// </summary>
    public int ShowSequence { get; set; } = 1;

    /// <summary>
    /// Whether the lookup is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
