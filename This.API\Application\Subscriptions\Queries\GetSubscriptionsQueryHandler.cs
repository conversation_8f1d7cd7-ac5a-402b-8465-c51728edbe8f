using Application.Subscriptions.DTOs;
using Application.Subscriptions.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.Subscriptions.Queries;

/// <summary>
/// Handler for getting subscriptions with pagination
/// </summary>
public class GetSubscriptionsQueryHandler : IRequestHandler<GetSubscriptionsQuery, PaginatedResult<SubscriptionDto>>
{
    private readonly IReadRepository<Subscription> _subscriptionRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetSubscriptionsQueryHandler(IReadRepository<Subscription> subscriptionRepository)
    {
        _subscriptionRepository = subscriptionRepository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<PaginatedResult<SubscriptionDto>> Handle(GetSubscriptionsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            // Calculate pagination
            var skip = (request.PageNumber - 1) * request.PageSize;

            // Create specifications for data and count
            var dataSpec = new SubscriptionsWithFiltersSpec(
                request.SearchTerm,
                request.ProductId,
                request.Status,
                request.IsActive,
                request.OrderBy,
                skip,
                request.PageSize);

            var countSpec = new SubscriptionsCountSpec(
                request.SearchTerm,
                request.ProductId,
                request.Status,
                request.IsActive);

            // Get data and count using specifications (tenant isolation handled by Finbuckle.MultiTenant)
            var subscriptions = await _subscriptionRepository.ListAsync(dataSpec, cancellationToken);
            var totalCount = await _subscriptionRepository.CountAsync(countSpec, cancellationToken);

            // Map to DTOs
            var subscriptionDtos = subscriptions.Select(s =>
            {
                var dto = s.Adapt<SubscriptionDto>();
                dto.ProductName = s.Product?.Name;
                dto.IsActive = s.IsActive && !s.IsDeleted;
                dto.MetadataCount = s.SubscriptionMetadata?.Count ?? 0;
                return dto;
            }).ToList();

            return new PaginatedResult<SubscriptionDto>(subscriptionDtos, request.PageNumber, request.PageSize, totalCount);
        }
        catch (Exception ex)
        {
            return PaginatedResult<SubscriptionDto>.Failure($"Failed to get subscriptions: {ex.Message}");
        }
    }
}
