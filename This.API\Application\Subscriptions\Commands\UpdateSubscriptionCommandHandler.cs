using Application.Subscriptions.DTOs;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;
using System.Text.Json;

namespace Application.Subscriptions.Commands;

/// <summary>
/// Handler for updating subscription
/// </summary>
public class UpdateSubscriptionCommandHandler : IRequestHandler<UpdateSubscriptionCommand, Result<SubscriptionDto>>
{
    private readonly IRepository<Subscription> _subscriptionRepository;
    private readonly IRepository<Product> _productRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpdateSubscriptionCommandHandler(
        IRepository<Subscription> subscriptionRepository,
        IRepository<Product> productRepository)
    {
        _subscriptionRepository = subscriptionRepository;
        _productRepository = productRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<SubscriptionDto>> Handle(UpdateSubscriptionCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Get existing subscription
            var subscription = await _subscriptionRepository.GetByIdAsync(request.Id, cancellationToken);
            if (subscription == null)
            {
                return Result<SubscriptionDto>.Failure("Subscription not found.");
            }

            // Verify product exists if ProductId is being changed
            if (subscription.ProductId != request.ProductId)
            {
                var product = await _productRepository.GetByIdAsync(request.ProductId, cancellationToken);
                if (product == null)
                {
                    return Result<SubscriptionDto>.Failure("Product not found.");
                }
            }

            // Update subscription properties
            subscription.ProductId = request.ProductId;
            subscription.SubscriptionType = request.SubscriptionType;
            subscription.Status = request.Status;
            subscription.StartDate = request.StartDate;
            subscription.EndDate = request.EndDate;
            subscription.AutoRenew = request.AutoRenew;
            subscription.PricingTier = request.PricingTier;
            subscription.Version = request.Version;
            subscription.TemplateJson = request.TemplateJson;
            subscription.TemplateDetails = request.TemplateDetails;
            subscription.IsActive = request.IsActive;

            await _subscriptionRepository.UpdateAsync(subscription, cancellationToken);

            // Get product for DTO
            var productEntity = await _productRepository.GetByIdAsync(subscription.ProductId, cancellationToken);

            // Map to DTO
            var subscriptionDto = subscription.Adapt<SubscriptionDto>();
            subscriptionDto.ProductName = productEntity?.Name;

            return Result<SubscriptionDto>.Success(subscriptionDto);
        }
        catch (Exception ex)
        {
            return Result<SubscriptionDto>.Failure($"Failed to update subscription: {ex.Message}");
        }
    }
}
