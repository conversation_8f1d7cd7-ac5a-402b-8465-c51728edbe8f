import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Form, Table, Badge, <PERSON><PERSON>, Spinner, Nav, Tab } from 'react-bootstrap';
import { Plus, Edit, Trash2, Search, Filter, Settings, Database, Users, Layers } from 'lucide-react';
import { contextService } from '../services/contextService';
import type { Context, ContextWithLookups, TenantContext, TenantContextWithLookups, ObjectLookup } from '../types/context';
import { ContextModal } from '../components/ContextManagement/ContextModal';
import { LookupManagementModal } from '../components/ContextManagement/LookupManagementModal';
import { TenantDropdown } from '../components/ContextManagement/TenantDropdown';
import { TenantContextModal } from '../components/ContextManagement/TenantContextModal';
import { TenantLookupManagementModal } from '../components/ContextManagement/TenantLookupManagementModal';

export const ContextManagement: React.FC = () => {
  // Tenant selection state
  const [selectedTenantId, setSelectedTenantId] = useState<string | null>(null);

  // Tab state
  const [activeTab, setActiveTab] = useState('context');

  // Context tab state
  const [contexts, setContexts] = useState<Context[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [categories, setCategories] = useState<string[]>([]);
  const [includeInactive, setIncludeInactive] = useState(false);

  // TenantContext tab state
  const [tenantContexts, setTenantContexts] = useState<TenantContext[]>([]);
  const [tenantLoading, setTenantLoading] = useState(false);
  const [tenantError, setTenantError] = useState<string | null>(null);
  const [tenantSearchTerm, setTenantSearchTerm] = useState('');
  const [selectedTenantCategory, setSelectedTenantCategory] = useState('');
  const [tenantCategories, setTenantCategories] = useState<string[]>([]);
  const [includeTenantInactive, setIncludeTenantInactive] = useState(false);

  // ObjectLookup tab state
  const [objectLookups, setObjectLookups] = useState<ObjectLookup[]>([]);
  const [objectLoading, setObjectLoading] = useState(false);
  const [objectError, setObjectError] = useState<string | null>(null);
  const [objectSearchTerm, setObjectSearchTerm] = useState('');
  const [selectedSourceType, setSelectedSourceType] = useState('');
  const [sourceTypes, setSourceTypes] = useState<string[]>([]);
  const [includeObjectInactive, setIncludeObjectInactive] = useState(false);

  // Modal states
  const [showContextModal, setShowContextModal] = useState(false);
  const [showLookupModal, setShowLookupModal] = useState(false);
  const [editingContext, setEditingContext] = useState<Context | null>(null);
  const [selectedContextForLookups, setSelectedContextForLookups] = useState<ContextWithLookups | null>(null);

  // TenantContext modal state
  const [showTenantContextModal, setShowTenantContextModal] = useState(false);
  const [showTenantLookupModal, setShowTenantLookupModal] = useState(false);
  const [editingTenantContext, setEditingTenantContext] = useState<TenantContext | null>(null);
  const [selectedTenantContextForLookups, setSelectedTenantContextForLookups] = useState<TenantContextWithLookups | null>(null);

  // TenantContext modal states
  const [showTenantContextModal, setShowTenantContextModal] = useState(false);
  const [showTenantLookupModal, setShowTenantLookupModal] = useState(false);
  const [editingTenantContext, setEditingTenantContext] = useState<TenantContext | null>(null);
  const [selectedTenantContextForLookups, setSelectedTenantContextForLookups] = useState<TenantContextWithLookups | null>(null);

  // ObjectLookup modal states
  const [showObjectLookupModal, setShowObjectLookupModal] = useState(false);
  const [editingObjectLookup, setEditingObjectLookup] = useState<ObjectLookup | null>(null);

  // Load data based on active tab
  useEffect(() => {
    if (activeTab === 'context') {
      loadContexts();
      loadCategories();
    } else if (activeTab === 'tenant-context') {
      loadTenantContexts();
      loadTenantCategories();
    } else if (activeTab === 'object-lookup') {
      loadObjectLookups();
      loadSourceTypes();
    }
  }, [activeTab, searchTerm, selectedCategory, includeInactive, tenantSearchTerm, selectedTenantCategory, includeTenantInactive, objectSearchTerm, selectedSourceType, includeObjectInactive, selectedTenantId]);

  const loadContexts = async () => {
    try {
      setLoading(true);
      const data = await contextService.getAllContexts(includeInactive, selectedCategory || undefined, searchTerm || undefined);
      setContexts(data);
      setError(null);
    } catch (err) {
      setError('Failed to load contexts');
      console.error('Error loading contexts:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const data = await contextService.getContextCategories();
      setCategories(data);
    } catch (err) {
      console.error('Error loading categories:', err);
    }
  };

  // TenantContext load functions
  const loadTenantContexts = async () => {
    if (!selectedTenantId) {
      setTenantError('Please select a tenant to view tenant contexts');
      setTenantContexts([]);
      return;
    }

    try {
      setTenantLoading(true);
      const data = await contextService.getAllTenantContexts(includeTenantInactive, selectedTenantCategory || undefined, tenantSearchTerm || undefined, selectedTenantId);
      setTenantContexts(data);
      setTenantError(null);
    } catch (err) {
      setTenantError('Failed to load tenant contexts');
      console.error('Error loading tenant contexts:', err);
    } finally {
      setTenantLoading(false);
    }
  };

  const loadTenantCategories = async () => {
    if (!selectedTenantId) {
      setTenantCategories([]);
      return;
    }

    try {
      const data = await contextService.getTenantContextCategories(selectedTenantId);
      setTenantCategories(data);
    } catch (err) {
      console.error('Error loading tenant categories:', err);
    }
  };

  // ObjectLookup load functions
  const loadObjectLookups = async () => {
    try {
      setObjectLoading(true);
      const data = await contextService.getAllObjectLookups(includeObjectInactive, selectedSourceType || undefined, objectSearchTerm || undefined);
      setObjectLookups(data);
      setObjectError(null);
    } catch (err) {
      setObjectError('Failed to load object lookups');
      console.error('Error loading object lookups:', err);
    } finally {
      setObjectLoading(false);
    }
  };

  const loadSourceTypes = async () => {
    try {
      const data = await contextService.getObjectLookupSourceTypes();
      setSourceTypes(data);
    } catch (err) {
      console.error('Error loading source types:', err);
    }
  };

  const handleCreateContext = () => {
    setEditingContext(null);
    setShowContextModal(true);
  };

  const handleEditContext = (context: Context) => {
    setEditingContext(context);
    setShowContextModal(true);
  };

  const handleContextSaved = () => {
    setShowContextModal(false);
    setEditingContext(null);
    loadContexts();
    loadCategories();
  };

  const handleManageLookups = async (context: Context) => {
    try {
      const contextWithLookups = await contextService.getContextWithLookups(context.id, true);
      setSelectedContextForLookups(contextWithLookups);
      setShowLookupModal(true);
    } catch (err) {
      setError('Failed to load context lookups');
      console.error('Error loading context lookups:', err);
    }
  };

  const handleLookupsSaved = () => {
    setShowLookupModal(false);
    setSelectedContextForLookups(null);
    // Optionally reload contexts if needed
  };

  // TenantContext handlers
  const handleCreateTenantContext = () => {
    setEditingTenantContext(null);
    setShowTenantContextModal(true);
  };

  const handleEditTenantContext = (tenantContext: TenantContext) => {
    setEditingTenantContext(tenantContext);
    setShowTenantContextModal(true);
  };

  const handleTenantContextSaved = () => {
    setShowTenantContextModal(false);
    setEditingTenantContext(null);
    loadTenantContexts();
    loadTenantCategories();
  };

  const handleManageTenantLookups = async (tenantContext: TenantContext) => {
    if (!selectedTenantId) {
      setTenantError('Please select a tenant first');
      return;
    }

    try {
      const tenantContextWithLookups = await contextService.getTenantContextWithLookups(tenantContext.id, true, selectedTenantId);
      setSelectedTenantContextForLookups(tenantContextWithLookups);
      setShowTenantLookupModal(true);
    } catch (err) {
      setTenantError('Failed to load tenant context lookups');
      console.error('Error loading tenant context lookups:', err);
    }
  };

  const handleTenantLookupsSaved = () => {
    setShowTenantLookupModal(false);
    setSelectedTenantContextForLookups(null);
  };

  // ObjectLookup handlers
  const handleCreateObjectLookup = () => {
    setEditingObjectLookup(null);
    setShowObjectLookupModal(true);
  };

  const handleEditObjectLookup = (objectLookup: ObjectLookup) => {
    setEditingObjectLookup(objectLookup);
    setShowObjectLookupModal(true);
  };

  const handleObjectLookupSaved = () => {
    setShowObjectLookupModal(false);
    setEditingObjectLookup(null);
    loadObjectLookups();
    loadSourceTypes();
  };

  // Filter functions for each tab
  const filteredContexts = contexts.filter(context => {
    const matchesSearch = !searchTerm ||
      context.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      context.description?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory = !selectedCategory || context.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  const filteredTenantContexts = tenantContexts.filter(tenantContext => {
    const matchesSearch = !tenantSearchTerm ||
      tenantContext.name.toLowerCase().includes(tenantSearchTerm.toLowerCase()) ||
      tenantContext.description?.toLowerCase().includes(tenantSearchTerm.toLowerCase());

    const matchesCategory = !selectedTenantCategory || tenantContext.category === selectedTenantCategory;

    return matchesSearch && matchesCategory;
  });

  const filteredObjectLookups = objectLookups.filter(objectLookup => {
    const matchesSearch = !objectSearchTerm ||
      objectLookup.name.toLowerCase().includes(objectSearchTerm.toLowerCase()) ||
      objectLookup.sourceType.toLowerCase().includes(objectSearchTerm.toLowerCase());

    const matchesSourceType = !selectedSourceType || objectLookup.sourceType === selectedSourceType;

    return matchesSearch && matchesSourceType;
  });

  return (
    <Container fluid className="p-4">
      <Row className="mb-4">
        <Col>
          <h2>Context Management</h2>
        </Col>
      </Row>

      {/* Tabbed Interface */}
      <Tab.Container activeKey={activeTab} onSelect={(k) => setActiveTab(k || 'context')}>
        <Row>
          <Col>
            <Nav variant="tabs" className="mb-4">
              <Nav.Item>
                <Nav.Link eventKey="context">
                  <Database size={16} className="me-2" />
                  Context & Lookup
                </Nav.Link>
              </Nav.Item>
              <Nav.Item>
                <Nav.Link eventKey="tenant-context">
                  <Users size={16} className="me-2" />
                  TenantContext & TenantLookup
                </Nav.Link>
              </Nav.Item>
              <Nav.Item>
                <Nav.Link eventKey="object-lookup">
                  <Layers size={16} className="me-2" />
                  ObjectLookup
                </Nav.Link>
              </Nav.Item>
            </Nav>

            <Tab.Content>
              {/* Context & Lookup Management Tab */}
              <Tab.Pane eventKey="context">
                <div className="d-flex justify-content-between align-items-center mb-4">
                  <h4>Context & Lookup Management</h4>
                  <Button variant="primary" onClick={handleCreateContext}>
                    <Plus size={16} className="me-2" />
                    Create Context
                  </Button>
                </div>

                {/* Context Filters */}
                <Row className="mb-4">
                  <Col md={4}>
                    <Form.Group>
                      <Form.Label>Search</Form.Label>
                      <div className="position-relative">
                        <Form.Control
                          type="text"
                          placeholder="Search contexts..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                        />
                        <Search size={16} className="position-absolute top-50 end-0 translate-middle-y me-3 text-muted" />
                      </div>
                    </Form.Group>
                  </Col>
                  <Col md={3}>
                    <Form.Group>
                      <Form.Label>Category</Form.Label>
                      <Form.Select
                        value={selectedCategory}
                        onChange={(e) => setSelectedCategory(e.target.value)}
                      >
                        <option value="">All Categories</option>
                        {categories.map(category => (
                          <option key={category} value={category}>{category}</option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={3}>
                    <Form.Group>
                      <Form.Label>&nbsp;</Form.Label>
                      <div>
                        <Form.Check
                          type="switch"
                          id="include-inactive"
                          label="Include Inactive"
                          checked={includeInactive}
                          onChange={(e) => setIncludeInactive(e.target.checked)}
                        />
                      </div>
                    </Form.Group>
                  </Col>
                  <Col md={2}>
                    <Form.Group>
                      <Form.Label>&nbsp;</Form.Label>
                      <div>
                        <Button variant="outline-secondary" onClick={loadContexts} disabled={loading}>
                          <Filter size={16} className="me-2" />
                          Refresh
                        </Button>
                      </div>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Context Error Alert */}
                {error && (
                  <Alert variant="danger" dismissible onClose={() => setError(null)}>
                    {error}
                  </Alert>
                )}

                {/* Contexts Table */}
                <Card>
                  <Card.Header>
                    <h5 className="mb-0">Contexts ({filteredContexts.length})</h5>
                  </Card.Header>
                  <Card.Body className="p-0">
                    {loading ? (
                      <div className="text-center p-4">
                        <Spinner animation="border" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </Spinner>
                      </div>
                    ) : (
                      <Table responsive hover className="mb-0">
                        <thead className="table-light">
                          <tr>
                            <th>Name</th>
                            <th>Description</th>
                            <th>Category</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {filteredContexts.length === 0 ? (
                            <tr>
                              <td colSpan={6} className="text-center text-muted p-4">
                                No contexts found
                              </td>
                            </tr>
                          ) : (
                            filteredContexts.map(context => (
                              <tr key={context.id}>
                                <td>
                                  <strong>{context.name}</strong>
                                </td>
                                <td>{context.description || '-'}</td>
                                <td>
                                  {context.category ? (
                                    <Badge bg="secondary">{context.category}</Badge>
                                  ) : (
                                    '-'
                                  )}
                                </td>
                                <td>
                                  <Badge bg={context.isActive ? 'success' : 'warning'}>
                                    {context.isActive ? 'Active' : 'Inactive'}
                                  </Badge>
                                </td>
                                <td>
                                  {new Date(context.createdAt).toLocaleDateString()}
                                </td>
                                <td>
                                  <div className="d-flex gap-2">
                                    <Button
                                      variant="outline-primary"
                                      size="sm"
                                      onClick={() => handleEditContext(context)}
                                      title="Edit Context"
                                    >
                                      <Edit size={14} />
                                    </Button>
                                    <Button
                                      variant="outline-info"
                                      size="sm"
                                      onClick={() => handleManageLookups(context)}
                                      title="Manage Lookups"
                                    >
                                      <Settings size={14} />
                                    </Button>
                                  </div>
                                </td>
                              </tr>
                            ))
                          )}
                        </tbody>
                      </Table>
                    )}
                  </Card.Body>
                </Card>
              </Tab.Pane>

              {/* TenantContext & TenantLookup Management Tab */}
              <Tab.Pane eventKey="tenant-context">
                <div className="d-flex justify-content-between align-items-center mb-4">
                  <div className="d-flex align-items-center gap-3">
                    <h4 className="mb-0">TenantContext & TenantLookup Management</h4>
                    <div style={{ minWidth: '300px' }}>
                      <TenantDropdown
                        required
                        value={selectedTenantId}
                        onChange={setSelectedTenantId}
                      />
                    </div>
                  </div>
                  <Button
                    variant="primary"
                    onClick={handleCreateTenantContext}
                    disabled={!selectedTenantId}
                  >
                    <Plus size={16} className="me-2" />
                    Create TenantContext
                  </Button>
                </div>

                {!selectedTenantId ? (
                  <Alert variant="info">
                    <Users size={16} className="me-2" />
                    Please select a tenant from the dropdown above to view and manage tenant-specific contexts and lookups.
                  </Alert>
                ) : (
                  <>
                    {/* TenantContext Filters */}
                    <Row className="mb-4">
                      <Col md={4}>
                        <Form.Group>
                          <Form.Label>Search</Form.Label>
                          <div className="position-relative">
                            <Form.Control
                              type="text"
                              placeholder="Search tenant contexts..."
                              value={tenantSearchTerm}
                              onChange={(e) => setTenantSearchTerm(e.target.value)}
                            />
                            <Search size={16} className="position-absolute top-50 end-0 translate-middle-y me-3 text-muted" />
                          </div>
                        </Form.Group>
                      </Col>
                      <Col md={3}>
                        <Form.Group>
                          <Form.Label>Category</Form.Label>
                          <Form.Select
                            value={selectedTenantCategory}
                            onChange={(e) => setSelectedTenantCategory(e.target.value)}
                          >
                            <option value="">All Categories</option>
                            {tenantCategories.map(category => (
                              <option key={category} value={category}>{category}</option>
                            ))}
                          </Form.Select>
                        </Form.Group>
                      </Col>
                      <Col md={3}>
                        <Form.Group>
                          <Form.Label>&nbsp;</Form.Label>
                          <div>
                            <Form.Check
                              type="switch"
                              id="include-tenant-inactive"
                              label="Include Inactive"
                              checked={includeTenantInactive}
                              onChange={(e) => setIncludeTenantInactive(e.target.checked)}
                            />
                          </div>
                        </Form.Group>
                      </Col>
                      <Col md={2}>
                        <Form.Group>
                          <Form.Label>&nbsp;</Form.Label>
                          <div>
                            <Button variant="outline-secondary" onClick={loadTenantContexts} disabled={tenantLoading}>
                              <Filter size={16} className="me-2" />
                              Refresh
                            </Button>
                          </div>
                        </Form.Group>
                      </Col>
                    </Row>

                    {/* TenantContext Error Alert */}
                    {tenantError && (
                      <Alert variant="danger" dismissible onClose={() => setTenantError(null)}>
                        {tenantError}
                      </Alert>
                    )}

                    {/* TenantContexts Table */}
                    <Card>
                      <Card.Header>
                        <h5 className="mb-0">TenantContexts ({filteredTenantContexts.length})</h5>
                      </Card.Header>
                      <Card.Body className="p-0">
                        {tenantLoading ? (
                          <div className="text-center p-4">
                            <Spinner animation="border" role="status">
                              <span className="visually-hidden">Loading...</span>
                            </Spinner>
                          </div>
                        ) : (
                          <Table responsive hover className="mb-0">
                            <thead className="table-light">
                              <tr>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Category</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                              </tr>
                            </thead>
                            <tbody>
                              {filteredTenantContexts.length === 0 ? (
                                <tr>
                                  <td colSpan={6} className="text-center text-muted p-4">
                                    No tenant contexts found
                                  </td>
                                </tr>
                              ) : (
                                filteredTenantContexts.map(tenantContext => (
                                  <tr key={tenantContext.id}>
                                    <td>
                                      <strong>{tenantContext.name}</strong>
                                    </td>
                                    <td>{tenantContext.description || '-'}</td>
                                    <td>
                                      {tenantContext.category ? (
                                        <Badge bg="secondary">{tenantContext.category}</Badge>
                                      ) : (
                                        '-'
                                      )}
                                    </td>
                                    <td>
                                      <Badge bg={tenantContext.isActive ? 'success' : 'warning'}>
                                        {tenantContext.isActive ? 'Active' : 'Inactive'}
                                      </Badge>
                                    </td>
                                    <td>
                                      {new Date(tenantContext.createdAt).toLocaleDateString()}
                                    </td>
                                    <td>
                                      <div className="d-flex gap-2">
                                        <Button
                                          variant="outline-primary"
                                          size="sm"
                                          onClick={() => handleEditTenantContext(tenantContext)}
                                          title="Edit TenantContext"
                                        >
                                          <Edit size={14} />
                                        </Button>
                                        <Button
                                          variant="outline-info"
                                          size="sm"
                                          onClick={() => handleManageTenantLookups(tenantContext)}
                                          title="Manage TenantLookups"
                                        >
                                          <Settings size={14} />
                                        </Button>
                                      </div>
                                    </td>
                                  </tr>
                                ))
                              )}
                            </tbody>
                          </Table>
                        )}
                      </Card.Body>
                    </Card>
                  </>
                )}
              </Tab.Pane>

              {/* ObjectLookup Management Tab */}
              <Tab.Pane eventKey="object-lookup">
                <div className="d-flex justify-content-between align-items-center mb-4">
                  <div className="d-flex align-items-center gap-3">
                    <h4 className="mb-0">ObjectLookup Management</h4>
                    <div style={{ minWidth: '300px' }}>
                      <TenantDropdown
                        value={selectedTenantId}
                        onChange={setSelectedTenantId}
                      />
                    </div>
                    <small className="text-muted">(Optional - filter by tenant)</small>
                  </div>
                  <Button variant="primary" onClick={handleCreateObjectLookup}>
                    <Plus size={16} className="me-2" />
                    Create ObjectLookup
                  </Button>
                </div>

                {/* ObjectLookup Filters */}
                <Row className="mb-4">
                  <Col md={4}>
                    <Form.Group>
                      <Form.Label>Search</Form.Label>
                      <div className="position-relative">
                        <Form.Control
                          type="text"
                          placeholder="Search object lookups..."
                          value={objectSearchTerm}
                          onChange={(e) => setObjectSearchTerm(e.target.value)}
                        />
                        <Search size={16} className="position-absolute top-50 end-0 translate-middle-y me-3 text-muted" />
                      </div>
                    </Form.Group>
                  </Col>
                  <Col md={3}>
                    <Form.Group>
                      <Form.Label>Source Type</Form.Label>
                      <Form.Select
                        value={selectedSourceType}
                        onChange={(e) => setSelectedSourceType(e.target.value)}
                      >
                        <option value="">All Source Types</option>
                        {sourceTypes.map(sourceType => (
                          <option key={sourceType} value={sourceType}>{sourceType}</option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={3}>
                    <Form.Group>
                      <Form.Label>&nbsp;</Form.Label>
                      <div>
                        <Form.Check
                          type="switch"
                          id="include-object-inactive"
                          label="Include Inactive"
                          checked={includeObjectInactive}
                          onChange={(e) => setIncludeObjectInactive(e.target.checked)}
                        />
                      </div>
                    </Form.Group>
                  </Col>
                  <Col md={2}>
                    <Form.Group>
                      <Form.Label>&nbsp;</Form.Label>
                      <div>
                        <Button variant="outline-secondary" onClick={loadObjectLookups} disabled={objectLoading}>
                          <Filter size={16} className="me-2" />
                          Refresh
                        </Button>
                      </div>
                    </Form.Group>
                  </Col>
                </Row>

                {/* ObjectLookup Error Alert */}
                {objectError && (
                  <Alert variant="danger" dismissible onClose={() => setObjectError(null)}>
                    {objectError}
                  </Alert>
                )}

                {/* ObjectLookups Table */}
                <Card>
                  <Card.Header>
                    <h5 className="mb-0">ObjectLookups ({filteredObjectLookups.length})</h5>
                  </Card.Header>
                  <Card.Body className="p-0">
                    {objectLoading ? (
                      <div className="text-center p-4">
                        <Spinner animation="border" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </Spinner>
                      </div>
                    ) : (
                      <Table responsive hover className="mb-0">
                        <thead className="table-light">
                          <tr>
                            <th>Name</th>
                            <th>Source Type</th>
                            <th>Display Field</th>
                            <th>Value Field</th>
                            <th>Tenant Filtering</th>
                            <th>Status</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {filteredObjectLookups.length === 0 ? (
                            <tr>
                              <td colSpan={7} className="text-center text-muted p-4">
                                No object lookups found
                              </td>
                            </tr>
                          ) : (
                            filteredObjectLookups.map(objectLookup => (
                              <tr key={objectLookup.id}>
                                <td>
                                  <strong>{objectLookup.name}</strong>
                                </td>
                                <td>
                                  <Badge bg="info">{objectLookup.sourceType}</Badge>
                                </td>
                                <td>{objectLookup.displayField}</td>
                                <td>{objectLookup.valueField}</td>
                                <td>
                                  <Badge bg={objectLookup.supportsTenantFiltering ? 'success' : 'secondary'}>
                                    {objectLookup.supportsTenantFiltering ? 'Yes' : 'No'}
                                  </Badge>
                                </td>
                                <td>
                                  <Badge bg={objectLookup.isActive ? 'success' : 'warning'}>
                                    {objectLookup.isActive ? 'Active' : 'Inactive'}
                                  </Badge>
                                </td>
                                <td>
                                  <div className="d-flex gap-2">
                                    <Button
                                      variant="outline-primary"
                                      size="sm"
                                      onClick={() => handleEditObjectLookup(objectLookup)}
                                      title="Edit ObjectLookup"
                                    >
                                      <Edit size={14} />
                                    </Button>
                                  </div>
                                </td>
                              </tr>
                            ))
                          )}
                        </tbody>
                      </Table>
                    )}
                  </Card.Body>
                </Card>
              </Tab.Pane>
            </Tab.Content>
          </Col>
        </Row>
      </Tab.Container>

      {/* Context Modal */}
      <ContextModal
        show={showContextModal}
        onHide={() => setShowContextModal(false)}
        context={editingContext}
        onSaved={handleContextSaved}
        categories={categories}
      />

      {/* Lookup Management Modal */}
      <LookupManagementModal
        show={showLookupModal}
        onHide={() => setShowLookupModal(false)}
        contextWithLookups={selectedContextForLookups}
        onSaved={handleLookupsSaved}
      />

      {/* TenantContext Modal */}
      <TenantContextModal
        show={showTenantContextModal}
        onHide={() => setShowTenantContextModal(false)}
        tenantContext={editingTenantContext}
        onSaved={handleTenantContextSaved}
        categories={tenantCategories}
        tenantId={selectedTenantId || ''}
      />

      {/* TenantLookup Management Modal */}
      <TenantLookupManagementModal
        show={showTenantLookupModal}
        onHide={() => setShowTenantLookupModal(false)}
        tenantContextWithLookups={selectedTenantContextForLookups}
        onSaved={handleTenantLookupsSaved}
        tenantId={selectedTenantId || ''}
      />
    </Container>
  );
};
