import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, But<PERSON>, Form, Alert, Spinner } from 'react-bootstrap';
import { Save, X } from 'lucide-react';
import { contextService } from '../../services/contextService';
import type { Context } from '../../types/context';

interface ContextModalProps {
  show: boolean;
  onHide: () => void;
  context: Context | null;
  onSaved: () => void;
  categories: string[];
}

export const ContextModal: React.FC<ContextModalProps> = ({
  show,
  onHide,
  context,
  onSaved,
  categories
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: '',
    isActive: true
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validated, setValidated] = useState(false);

  const isEditing = !!context;

  useEffect(() => {
    if (show) {
      if (context) {
        setFormData({
          name: context.name,
          description: context.description || '',
          category: context.category || '',
          isActive: context.isActive
        });
      } else {
        setFormData({
          name: '',
          description: '',
          category: '',
          isActive: true
        });
      }
      setError(null);
      setValidated(false);
    }
  }, [show, context]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const form = e.currentTarget as HTMLFormElement;
    
    if (form.checkValidity() === false) {
      e.stopPropagation();
      setValidated(true);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      if (isEditing) {
        await contextService.updateContext({
          id: context!.id,
          name: formData.name.trim(),
          description: formData.description.trim() || undefined,
          category: formData.category.trim() || undefined,
          isActive: formData.isActive
        });
      } else {
        await contextService.createContext({
          name: formData.name.trim(),
          description: formData.description.trim() || undefined,
          category: formData.category.trim() || undefined,
          isActive: formData.isActive
        });
      }

      onSaved();
    } catch (err: any) {
      setError(err.response?.data?.message || err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (validated) {
      setValidated(false);
    }
  };

  return (
    <Modal show={show} onHide={onHide} size="lg">
      <Modal.Header closeButton>
        <Modal.Title>
          {isEditing ? 'Edit Context' : 'Create New Context'}
        </Modal.Title>
      </Modal.Header>

      <Form noValidate validated={validated} onSubmit={handleSubmit}>
        <Modal.Body>
          {error && (
            <Alert variant="danger" dismissible onClose={() => setError(null)}>
              {error}
            </Alert>
          )}

          <Form.Group className="mb-3">
            <Form.Label>Name <span className="text-danger">*</span></Form.Label>
            <Form.Control
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Enter context name"
              required
              disabled={loading}
            />
            <Form.Control.Feedback type="invalid">
              Please provide a valid context name.
            </Form.Control.Feedback>
            <Form.Text className="text-muted">
              A unique name to identify this context
            </Form.Text>
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Description</Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Enter context description (optional)"
              disabled={loading}
            />
            <Form.Text className="text-muted">
              Optional description to explain the purpose of this context
            </Form.Text>
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Category</Form.Label>
            <Form.Control
              type="text"
              list="categories"
              value={formData.category}
              onChange={(e) => handleInputChange('category', e.target.value)}
              placeholder="Enter or select category (optional)"
              disabled={loading}
            />
            <datalist id="categories">
              {categories.map(category => (
                <option key={category} value={category} />
              ))}
            </datalist>
            <Form.Text className="text-muted">
              Optional category for grouping related contexts
            </Form.Text>
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Check
              type="switch"
              id="context-active-switch"
              label="Active"
              checked={formData.isActive}
              onChange={(e) => handleInputChange('isActive', e.target.checked)}
              disabled={loading}
            />
            <Form.Text className="text-muted">
              Inactive contexts are hidden from most operations
            </Form.Text>
          </Form.Group>
        </Modal.Body>

        <Modal.Footer>
          <Button variant="secondary" onClick={onHide} disabled={loading}>
            <X size={16} className="me-2" />
            Cancel
          </Button>
          <Button variant="primary" type="submit" disabled={loading}>
            {loading ? (
              <Spinner animation="border" size="sm" className="me-2" />
            ) : (
              <Save size={16} className="me-2" />
            )}
            {isEditing ? 'Update Context' : 'Create Context'}
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};
