import React from 'react'
import { cn } from '@/shared/utils/utils'

export interface RadioOption {
  value: string
  label: string
  description?: string
}

export interface RadioGroupProps {
  name: string
  value?: string
  onChange?: (value: string) => void
  options: RadioOption[]
  className?: string
  error?: boolean
  disabled?: boolean
}

const RadioGroup = React.forwardRef<HTMLDivElement, RadioGroupProps>(
  ({ className, name, value, onChange, options, error, disabled, ...props }, ref) => {
    return (
      <div ref={ref} className={cn('space-y-3', className)} {...props}>
        {options.map((option) => (
          <label
            key={option.value}
            className={cn(
              'flex items-start space-x-3 cursor-pointer',
              disabled && 'cursor-not-allowed opacity-50'
            )}
          >
            <input
              type="radio"
              name={name}
              value={option.value}
              checked={value === option.value}
              onChange={(e) => onChange?.(e.target.value)}
              disabled={disabled}
              className={cn(
                'mt-1 h-4 w-4 text-primary border-input focus:ring-2 focus:ring-ring focus:ring-offset-2',
                error && 'border-destructive focus:ring-destructive'
              )}
            />
            <div className="flex-1">
              <div className="text-sm font-medium">{option.label}</div>
              {option.description && (
                <div className="text-xs text-muted-foreground">{option.description}</div>
              )}
            </div>
          </label>
        ))}
      </div>
    )
  }
)

RadioGroup.displayName = 'RadioGroup'

export { RadioGroup }
