{"objectName": "Booking", "tenantId": "black", "viewName": "blackBooking", "viewCreationResult": "View created and queried successfully", "columnNames": ["RefId", "TenantId", "ParentObjectValueId", "SpecialTermsNotes", "PaymentPlan", "Status", "AgreementDocument", "UnitApartment", "BookingDate", "Customer", "BookingID", "CreatedAt", "ModifiedAt"], "viewData": [{"RefId": "71409b28-1cbc-4502-9189-45d641951104", "TenantId": "black", "ParentObjectValueId": null, "SpecialTermsNotes": null, "PaymentPlan": "2024-12-30", "Status": null, "AgreementDocument": "1251171", "UnitApartment": null, "BookingDate": null, "Customer": null, "BookingID": null, "CreatedAt": "2025-06-11T16:56:58.583228Z", "ModifiedAt": "2025-06-11T16:56:58.6848Z"}, {"RefId": "5056b698-a082-40a3-a818-3d4906d7979a", "TenantId": "black", "ParentObjectValueId": "f866ac84-d952-42e0-b57f-f68419e91755", "SpecialTermsNotes": "Booking #5 - Customer selected premium unit with special terms", "PaymentPlan": "50-50 Payment Plan", "Status": null, "AgreementDocument": "https://agreements.com/booking-BK-2024-005.pdf", "UnitApartment": "Unit 4392 - 2BR City View", "BookingDate": "2025-06-10", "Customer": "<PERSON>", "BookingID": "BK-2024-005", "CreatedAt": "2025-06-11T16:54:14.768565Z", "ModifiedAt": "2025-06-11T16:54:15.398101Z"}, {"RefId": "f0a8b603-938c-4a16-947a-3a8c9e09e890", "TenantId": "black", "ParentObjectValueId": "adcb2d7a-b052-4b0f-8596-fc9a399c5d5d", "SpecialTermsNotes": "Booking #4 - Customer selected premium unit with special terms", "PaymentPlan": "60-40 Payment Plan", "Status": null, "AgreementDocument": "https://agreements.com/booking-BK-2024-004.pdf", "UnitApartment": "Unit 2714 - 1BR Marina View", "BookingDate": "2025-03-28", "Customer": "<PERSON>", "BookingID": "BK-2024-004", "CreatedAt": "2025-06-11T16:54:13.977012Z", "ModifiedAt": "2025-06-11T16:54:14.671056Z"}, {"RefId": "d2519288-d326-42c1-8c37-2374d8563e94", "TenantId": "black", "ParentObjectValueId": "f866ac84-d952-42e0-b57f-f68419e91755", "SpecialTermsNotes": "Booking #3 - Customer selected premium unit with special terms", "PaymentPlan": "70-30 Payment Plan", "Status": null, "AgreementDocument": "https://agreements.com/booking-BK-2024-003.pdf", "UnitApartment": "Unit 2539 - 4BR Luxury Suite", "BookingDate": "2025-04-25", "Customer": "<PERSON>", "BookingID": "BK-2024-003", "CreatedAt": "2025-06-11T16:54:13.179345Z", "ModifiedAt": "2025-06-11T16:54:13.850951Z"}, {"RefId": "a0a0d950-df01-4750-ae92-babb71e39d04", "TenantId": "black", "ParentObjectValueId": "adcb2d7a-b052-4b0f-8596-fc9a399c5d5d", "SpecialTermsNotes": "Booking #2 - Customer selected premium unit with special terms", "PaymentPlan": "50-50 Payment Plan", "Status": null, "AgreementDocument": "https://agreements.com/booking-BK-2024-002.pdf", "UnitApartment": "Unit 4237 - 3BR Penthouse", "BookingDate": "2025-03-23", "Customer": "<PERSON>", "BookingID": "BK-2024-002", "CreatedAt": "2025-06-11T16:54:12.338531Z", "ModifiedAt": "2025-06-11T16:54:13.076848Z"}, {"RefId": "7784839c-961f-49ce-8a28-27bc9a952043", "TenantId": "black", "ParentObjectValueId": "f866ac84-d952-42e0-b57f-f68419e91755", "SpecialTermsNotes": "Booking #1 - Customer selected premium unit with special terms", "PaymentPlan": "60-40 Payment Plan", "Status": null, "AgreementDocument": "https://agreements.com/booking-BK-2024-001.pdf", "UnitApartment": "Unit 2289 - 2BR City View", "BookingDate": "2025-04-26", "Customer": "<PERSON>", "BookingID": "BK-2024-001", "CreatedAt": "2025-06-11T16:54:11.555057Z", "ModifiedAt": "2025-06-11T16:54:12.213408Z"}], "totalRows": 6, "pageNumber": 1, "pageSize": 10, "totalPages": 1, "hasPreviousPage": false, "hasNextPage": false, "message": "Successfully retrieved 6 of 6 rows from view 'blackBooking' (Page 1 of 1)"}