/* Responsive Form Layout for UpsertObject */

/* Base responsive form grid layout */
.upsert-form-grid {
  display: grid;
  gap: 1rem;
  width: 100%;
}

/* Tablet: 2 columns */
@media (min-width: 768px) and (max-width: 1023px) {
  .upsert-form-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }
}

/* Small Desktop: 3 columns */
@media (min-width: 1024px) and (max-width: 1399px) {
  .upsert-form-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
}

/* Large Desktop: 4 columns */
@media (min-width: 1400px) and (max-width: 1799px) {
  .upsert-form-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }
}

/* Extra Large Desktop: 5 columns */
@media (min-width: 1800px) {
  .upsert-form-grid {
    grid-template-columns: repeat(5, 1fr);
    gap: 1.75rem;
  }
}

/* Mobile layout: 1 column for small screens */
@media (max-width: 767px) {
  .upsert-form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Form field wrapper for consistent spacing */
.upsert-form-field {
  display: flex;
  flex-direction: column;
  min-width: 0; /* Prevent overflow */
  position: relative;
}

/* Ensure form fields within the grid maintain proper spacing */
.upsert-form-grid .upsert-form-field {
  margin-bottom: 0; /* Remove default margin since grid handles spacing */
}

/* Override the default space-y-6 for responsive layout */
.upsert-form-grid .space-y-2 > * + * {
  margin-top: 0;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 640px) {
  .upsert-form-grid {
    gap: 0.75rem;
  }
}

/* Enhanced spacing for larger screens */
@media (min-width: 1400px) {
  .upsert-form-grid {
    gap: 1.75rem;
  }
}

@media (min-width: 1800px) {
  .upsert-form-grid {
    gap: 2rem;
  }
}

/* Animation for smooth transitions when switching layouts */
.upsert-form-grid {
  animation: fadeInGrid 0.3s ease-in-out;
}

@keyframes fadeInGrid {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Ensure proper spacing in form sections */
.upsert-form-grid .upsert-form-field:last-child {
  margin-bottom: 0;
}

/* Special handling for wide fields that should span multiple columns */
.upsert-form-field.span-full {
  grid-column: 1 / -1;
}

/* For textarea and other wide inputs - responsive spanning */
.upsert-form-field.span-two {
  grid-column: span 2;
}

.upsert-form-field.span-three {
  grid-column: span 3;
}

.upsert-form-field.span-half {
  grid-column: span 2;
}

/* Tablet (2 columns): span-two and span-three become span 1 */
@media (min-width: 768px) and (max-width: 1023px) {
  .upsert-form-field.span-two,
  .upsert-form-field.span-three,
  .upsert-form-field.span-half {
    grid-column: span 1;
  }
}

/* Small Desktop (3 columns): span-half becomes span 2, span-three stays 3 */
@media (min-width: 1024px) and (max-width: 1399px) {
  .upsert-form-field.span-half {
    grid-column: span 2;
  }
  .upsert-form-field.span-two {
    grid-column: span 2;
  }
  .upsert-form-field.span-three {
    grid-column: span 3;
  }
}

/* Large Desktop (4 columns): optimize spanning */
@media (min-width: 1400px) and (max-width: 1799px) {
  .upsert-form-field.span-half {
    grid-column: span 2;
  }
  .upsert-form-field.span-two {
    grid-column: span 2;
  }
  .upsert-form-field.span-three {
    grid-column: span 3;
  }
}

/* Extra Large Desktop (5 columns): optimize spanning */
@media (min-width: 1800px) {
  .upsert-form-field.span-half {
    grid-column: span 2;
  }
  .upsert-form-field.span-two {
    grid-column: span 2;
  }
  .upsert-form-field.span-three {
    grid-column: span 3;
  }
}

/* On mobile, all fields span full width */
@media (max-width: 767px) {
  .upsert-form-field.span-two,
  .upsert-form-field.span-three,
  .upsert-form-field.span-half,
  .upsert-form-field.span-full {
    grid-column: 1;
  }
}

/* Maintain consistent field heights within rows */
.upsert-form-grid .upsert-form-field {
  align-self: start;
}

/* Loading state overlay positioning */
.upsert-form-field .absolute {
  border-radius: 0.5rem;
}

/* Error message positioning for grid layout */
.upsert-form-field .text-destructive {
  margin-top: 0.25rem;
  font-size: 0.875rem;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .upsert-form-grid {
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .upsert-form-grid {
    gap: 1.5rem;
  }
}

/* Focus management for grid layout */
.upsert-form-grid .upsert-form-field:focus-within {
  z-index: 1;
}

/* Ensure proper stacking context for validation messages */
.upsert-form-field {
  z-index: 0;
}

.upsert-form-field:focus-within {
  z-index: 1;
}

/* Integration with existing form components */
.upsert-form-grid .text-input-container {
  width: 100%;
  margin-bottom: 0;
}

.upsert-form-grid .text-input-wrapper {
  margin-bottom: 0;
}

/* Ensure dropdowns and selects work well in grid */
.upsert-form-grid select,
.upsert-form-grid .select-container {
  width: 100%;
}

/* File upload components in grid */
.upsert-form-grid .file-input-container {
  width: 100%;
}

/* Checkbox and radio groups in grid */
.upsert-form-grid .checkbox-options-horizontal,
.upsert-form-grid .radio-options-horizontal {
  flex-wrap: wrap;
  gap: 0.75rem;
}

/* Date and time inputs */
.upsert-form-grid input[type="date"],
.upsert-form-grid input[type="datetime-local"],
.upsert-form-grid input[type="time"] {
  width: 100%;
}

/* Ensure consistent label spacing in grid */
.upsert-form-grid .text-input-label {
  margin-bottom: 0.25rem;
}

/* Handle validation messages in grid layout */
.upsert-form-grid .text-input-errors {
  margin-top: 0.25rem;
}

/* Responsive typography adjustments */
@media (max-width: 640px) {
  .upsert-form-grid .text-input-label {
    font-size: 0.8125rem;
  }

  .upsert-form-grid .text-input,
  .upsert-form-grid select {
    font-size: 0.875rem;
    padding: 0.625rem 0.75rem;
  }
}

/* Enhanced typography for larger screens */
@media (min-width: 1400px) {
  .upsert-form-grid .text-input-label {
    font-size: 0.9375rem;
    font-weight: 500;
    margin-bottom: 0.375rem;
  }

  .upsert-form-grid .text-input,
  .upsert-form-grid select {
    font-size: 0.9375rem;
    padding: 0.75rem 1rem;
    line-height: 1.5;
  }
}

@media (min-width: 1800px) {
  .upsert-form-grid .text-input-label {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }

  .upsert-form-grid .text-input,
  .upsert-form-grid select {
    font-size: 1rem;
    padding: 0.875rem 1.125rem;
  }
}

/* Improved field minimum widths for larger screens */
@media (min-width: 1400px) {
  .upsert-form-field {
    min-width: 200px;
  }
}

@media (min-width: 1800px) {
  .upsert-form-field {
    min-width: 220px;
  }
}

/* Enhanced focus states for larger screens */
@media (min-width: 1400px) {
  .upsert-form-grid .text-input:focus,
  .upsert-form-grid select:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 0 0 1px rgba(59, 130, 246, 0.5);
  }
}

/* Maximum width constraints for ultrawide displays */
@media (min-width: 2400px) {
  .upsert-form-grid {
    max-width: 2200px;
    margin: 0 auto;
  }
}

/* Ensure proper alignment for odd numbers of fields */
.upsert-form-grid {
  align-items: start;
  justify-items: stretch;
}

/* Handle empty grid areas gracefully */
.upsert-form-field:empty {
  display: none;
}

/* Improved visual hierarchy for larger screens */
@media (min-width: 1400px) {
  .upsert-form-grid .text-input-container {
    position: relative;
  }

  .upsert-form-grid .text-input-label {
    position: relative;
    z-index: 1;
  }
}

/* Ensure consistent field heights across columns */
.upsert-form-grid .upsert-form-field {
  display: flex;
  flex-direction: column;
  height: fit-content;
}

/* Better spacing for validation messages on larger screens */
@media (min-width: 1400px) {
  .upsert-form-field .text-destructive {
    margin-top: 0.375rem;
    font-size: 0.9375rem;
  }
}

/* Responsive card padding adjustments */
@media (min-width: 1400px) {
  .upsert-form-grid {
    padding: 0.5rem;
  }
}

@media (min-width: 1800px) {
  .upsert-form-grid {
    padding: 0.75rem;
  }
}
