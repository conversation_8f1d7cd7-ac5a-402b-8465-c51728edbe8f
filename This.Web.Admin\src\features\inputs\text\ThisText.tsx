// components/ThisText.tsx
import React, { useState } from 'react';

interface ThisTextProps {
  id: string;
  label: string;
  type?: string;
  placeholder?: string;
  required?: boolean;
  maxLength?: number;
  minLength?: number;
  pattern?: string;
  value: string;
  onChange: (value: string) => void;
  onValidation?: (errors: string[]) => void;
  disabled?: boolean;
  readOnly?: boolean;
  helpText?: string;
  showCharacterCount?: boolean;
  allowClear?: boolean;
  autoComplete?: string;
  autoFocus?: boolean;
  spellCheck?: boolean;
  customValidation?: (value: string) => string | null;
}

interface ValidationRule {
  test: (value: string) => boolean;
  message: string;
}

const ThisText: React.FC<ThisTextProps> = ({
  id,
  label,
  type = 'text',
  placeholder = '',
  required = false,
  maxLength,
  minLength,
  pattern,
  value,
  onChange,
  onValidation,
  disabled = false,
  readOnly = false,
  helpText,
  showCharacterCount = false,
  allowClear = false,
  autoComplete,
  autoFocus = false,
  spellCheck = true,
  customValidation
}) => {
  const [errors, setErrors] = useState<string[]>([]);

  // Validation rules in priority order (most important first)
  const getValidationRules = (): ValidationRule[] => {
    const rules: ValidationRule[] = [];

    // 1. Required validation (highest priority)
    if (required) {
      rules.push({
        test: (val) => val.trim().length > 0,
        message: `${label} is required`
      });
    }

    // Only validate format if field has content
    if (required || value.trim().length > 0) {
      // 2. Minimum length validation
      if (minLength) {
        rules.push({
          test: (val) => !val || val.length >= minLength,
          message: `Minimum ${minLength} characters required`
        });
      }

      // 3. Pattern validation (format errors)
      if (pattern) {
        const regex = new RegExp(pattern);
        rules.push({
          test: (val) => !val || regex.test(val),
          message: getPatternErrorMessage()
        });
      }

      // 4. Email validation (specific format)
      if (type === 'email') {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        rules.push({
          test: (val) => !val || emailRegex.test(val),
          message: 'Enter a valid email address'
        });
      }

      // 5. Custom validation
      if (customValidation) {
        rules.push({
          test: (val) => {
            const error = customValidation(val);
            return error === null;
          },
          message: customValidation(value) || 'Invalid value'
        });
      }

      // 6. Maximum length validation (lowest priority)
      if (maxLength) {
        rules.push({
          test: (val) => val.length <= maxLength,
          message: `Maximum ${maxLength} characters allowed`
        });
      }
    }

    return rules;
  };

  const getPatternErrorMessage = (): string => {
    if (id.includes('name') || id.includes('Name')) {
      return 'Only letters and spaces allowed';
    }
    if (id.includes('phone') || id.includes('Phone')) {
      return 'Enter a valid phone number';
    }
    return 'Invalid format';
  };

  const validateValue = (val: string): string[] => {
    const rules = getValidationRules();

    // Return only the first error found (most important)
    for (const rule of rules) {
      if (!rule.test(val)) {
        return [rule.message];
      }
    }

    return [];
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);

    // Real-time validation
    const newErrors = validateValue(newValue);
    setErrors(newErrors);

    // Notify parent of validation state
    onValidation?.(newErrors);
  };

  const handleBlur = () => {
    // Validate on blur for final check
    const newErrors = validateValue(value);
    setErrors(newErrors);

    // Notify parent of validation state
    onValidation?.(newErrors);
  };

  const hasErrors = errors.length > 0;

  return (
    <div className="text-input-container">
      {/* Label */}
      <label htmlFor={id} className="text-input-label">
        {label}
        {required && <span className="required-indicator">*</span>}
        {helpText && (
          <span
            className="text-input-info-icon"
            data-tooltip={helpText}
            aria-label={helpText}
          />
        )}
      </label>

      {/* Input Field */}
      <div className="text-input-wrapper">
        <input
          id={id}
          type={type}
          value={value}
          placeholder={placeholder}
          disabled={disabled}
          readOnly={readOnly}
          onChange={handleChange}
          onBlur={handleBlur}
          className={`text-input ${hasErrors ? 'has-error' : ''} ${disabled ? 'disabled' : ''} ${maxLength ? 'has-character-count' : ''}`}
          maxLength={maxLength}
          minLength={minLength}
          aria-invalid={hasErrors ? 'true' : 'false'}
          aria-describedby={helpText ? `${id}-help` : undefined}
          autoComplete={autoComplete}
          autoFocus={autoFocus}
          spellCheck={spellCheck}
        />

        {/* Character Count - Inside input field */}
        {showCharacterCount && maxLength && !disabled && !readOnly && (
          <span className={`character-count ${value.length > maxLength * 0.8 ? 'warning' : ''} ${value.length >= maxLength ? 'error' : ''}`}>
            {value.length} / {maxLength}
          </span>
        )}

        {/* Clear Button */}
        {allowClear && value && !disabled && !readOnly && (
          <button
            type="button"
            className="text-input-clear-button"
            onClick={() => onChange('')}
            aria-label="Clear input"
            title="Clear input"
          >
            ×
          </button>
        )}

        {/* Error Message - Absolutely positioned */}
        {hasErrors && (
          <div className="text-input-errors" role="alert">
            <p className="error-message">
              {errors[0]}
            </p>
          </div>
        )}
      </div>




    </div>
  );
};

export default ThisText;
