// components/ThisNumber.tsx
import React, { useState } from 'react';

interface ThisNumberProps {
  id: string;
  label: string;
  placeholder?: string;
  required?: boolean;
  min?: number;
  max?: number;
  step?: number;
  decimals?: number;
  value: string;
  onChange: (value: string) => void;
  onValidation?: (errors: string[]) => void;
  disabled?: boolean;
  readOnly?: boolean;
  helpText?: string;
  allowNegative?: boolean;
  thousandsSeparator?: boolean;
  currency?: string;
}

interface ValidationRule {
  test: (value: string) => boolean;
  message: string;
}

const ThisNumber: React.FC<ThisNumberProps> = ({
  id,
  label,
  placeholder = '',
  required = false,
  min,
  max,
  step,
  decimals,
  value,
  onChange,
  onValidation,
  disabled = false,
  readOnly = false,
  helpText,
  allowNegative = true,
  thousandsSeparator = false,
  currency
}) => {
  const [errors, setErrors] = useState<string[]>([]);

  // Helper function to parse number from string
  const parseNumber = (val: string): number | null => {
    if (!val || val.trim() === '') return null;
    
    // Remove thousands separators and currency symbols for parsing
    let cleanValue = val.replace(/,/g, '');
    if (currency) {
      cleanValue = cleanValue.replace(new RegExp(`\\${currency}`, 'g'), '');
    }
    cleanValue = cleanValue.trim();
    
    const num = parseFloat(cleanValue);
    return isNaN(num) ? null : num;
  };

  // Validation rules in priority order (most important first)
  const getValidationRules = (): ValidationRule[] => {
    const rules: ValidationRule[] = [];

    // 1. Required validation (highest priority)
    if (required) {
      rules.push({
        test: (val) => val.trim().length > 0,
        message: `${label} is required`
      });
    }

    // Only validate format if field has content
    if (required || value.trim().length > 0) {
      // 2. Number format validation
      rules.push({
        test: (val) => {
          if (!val || val.trim() === '') return true;
          const num = parseNumber(val);
          return num !== null;
        },
        message: 'Enter a valid number'
      });

      // 3. Negative number validation
      if (!allowNegative) {
        rules.push({
          test: (val) => {
            const num = parseNumber(val);
            return num === null || num >= 0;
          },
          message: 'Negative numbers are not allowed'
        });
      }

      // 4. Minimum value validation
      if (min !== undefined) {
        rules.push({
          test: (val) => {
            const num = parseNumber(val);
            return num === null || num >= min;
          },
          message: `Minimum value is ${min}`
        });
      }

      // 5. Maximum value validation
      if (max !== undefined) {
        rules.push({
          test: (val) => {
            const num = parseNumber(val);
            return num === null || num <= max;
          },
          message: `Maximum value is ${max}`
        });
      }

      // 6. Decimal places validation
      if (decimals !== undefined) {
        rules.push({
          test: (val) => {
            const num = parseNumber(val);
            if (num === null) return true;
            
            const decimalPart = val.split('.')[1];
            return !decimalPart || decimalPart.length <= decimals;
          },
          message: `Maximum ${decimals} decimal places allowed`
        });
      }

      // 7. Step validation
      if (step !== undefined && min !== undefined) {
        rules.push({
          test: (val) => {
            const num = parseNumber(val);
            if (num === null) return true;
            
            const remainder = (num - min) % step;
            return Math.abs(remainder) < 0.0001; // Account for floating point precision
          },
          message: `Value must be in increments of ${step}`
        });
      }
    }

    return rules;
  };

  const validateValue = (val: string): string[] => {
    const rules = getValidationRules();

    // Return only the first error found (most important)
    for (const rule of rules) {
      if (!rule.test(val)) {
        return [rule.message];
      }
    }

    return [];
  };

  // Format number for display
  const formatNumber = (val: string): string => {
    if (!val || val.trim() === '') return val;
    
    const num = parseNumber(val);
    if (num === null) return val;

    let formatted = num.toString();

    // Add thousands separators
    if (thousandsSeparator) {
      const parts = formatted.split('.');
      parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      formatted = parts.join('.');
    }

    // Add currency symbol
    if (currency) {
      formatted = `${currency}${formatted}`;
    }

    return formatted;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let newValue = e.target.value;

    // Allow typing intermediate states (like "-", ".", "1.")
    // but validate the final result
    onChange(newValue);

    // Real-time validation
    const newErrors = validateValue(newValue);
    setErrors(newErrors);

    // Notify parent of validation state
    onValidation?.(newErrors);
  };

  const handleBlur = () => {
    // Format the number on blur if it's valid
    let formattedValue = value;
    const num = parseNumber(value);
    
    if (num !== null && !errors.length) {
      formattedValue = formatNumber(value);
      if (formattedValue !== value) {
        onChange(formattedValue);
      }
    }

    // Validate on blur for final check
    const newErrors = validateValue(formattedValue);
    setErrors(newErrors);

    // Notify parent of validation state
    onValidation?.(newErrors);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Allow: backspace, delete, tab, escape, enter
    if ([8, 9, 27, 13, 46].indexOf(e.keyCode) !== -1 ||
        // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
        (e.keyCode === 65 && e.ctrlKey === true) ||
        (e.keyCode === 67 && e.ctrlKey === true) ||
        (e.keyCode === 86 && e.ctrlKey === true) ||
        (e.keyCode === 88 && e.ctrlKey === true) ||
        // Allow: home, end, left, right
        (e.keyCode >= 35 && e.keyCode <= 39)) {
      return;
    }

    // Ensure that it is a number and stop the keypress
    if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
      // Allow decimal point
      if (e.keyCode === 190 || e.keyCode === 110) {
        // Only allow one decimal point
        if (value.indexOf('.') !== -1) {
          e.preventDefault();
        }
        return;
      }
      
      // Allow minus sign at the beginning
      if ((e.keyCode === 189 || e.keyCode === 109) && allowNegative) {
        if (value.indexOf('-') !== -1 || (e.target as HTMLInputElement).selectionStart !== 0) {
          e.preventDefault();
        }
        return;
      }
      
      e.preventDefault();
    }
  };

  const hasErrors = errors.length > 0;

  // Generate placeholder text
  const getPlaceholder = (): string => {
    if (placeholder) return placeholder;
    
    let placeholderText = '';
    if (currency) placeholderText += currency;
    
    if (min !== undefined && max !== undefined) {
      placeholderText += `${min} - ${max}`;
    } else if (min !== undefined) {
      placeholderText += `Min: ${min}`;
    } else if (max !== undefined) {
      placeholderText += `Max: ${max}`;
    } else {
      placeholderText += 'Enter a number';
    }
    
    return placeholderText;
  };

  return (
    <div className="text-input-container">
      {/* Label */}
      <label htmlFor={id} className="text-input-label">
        {label}
        {required && <span className="required-indicator">*</span>}
        {helpText && (
          <span
            className="text-input-info-icon"
            data-tooltip={helpText}
            aria-label={helpText}
          />
        )}
      </label>

      {/* Input Field */}
      <div className="text-input-wrapper">
        <input
          id={id}
          type="text"
          inputMode="numeric"
          value={value}
          placeholder={getPlaceholder()}
          disabled={disabled}
          readOnly={readOnly}
          onChange={handleChange}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          className={`text-input ${hasErrors ? 'has-error' : ''} ${disabled ? 'disabled' : ''}`}
          aria-invalid={hasErrors ? 'true' : 'false'}
          aria-describedby={helpText ? `${id}-help` : undefined}
          min={min}
          max={max}
          step={step}
        />

        {/* Error Message - Absolutely positioned */}
        {hasErrors && (
          <div className="text-input-errors" role="alert">
            <p className="error-message">
              {errors[0]}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ThisNumber;
