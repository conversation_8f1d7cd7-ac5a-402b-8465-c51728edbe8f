using Application.Context.Specifications;
using Domain.Entities;
using Infrastructure.Database.Repositories.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Context.Commands.UpsertLookup;

/// <summary>
/// Handler for UpsertLookupCommand
/// </summary>
public class UpsertLookupCommandHandler : IRequestHandler<UpsertLookupCommand, Result<Guid>>
{
    private readonly IRepository<Lookup> _lookupRepository;
    private readonly IRepository<Context> _contextRepository;
    private readonly ILogger<UpsertLookupCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpsertLookupCommandHandler(
        IRepository<Lookup> lookupRepository,
        IRepository<Context> contextRepository,
        ILogger<UpsertLookupCommandHandler> logger)
    {
        _lookupRepository = lookupRepository;
        _contextRepository = contextRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<Guid>> Handle(UpsertLookupCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Validate that the context exists
            var context = await _contextRepository.GetByIdAsync(request.ContextId, cancellationToken);
            if (context == null)
            {
                return Result<Guid>.Failure($"Context with ID '{request.ContextId}' not found.");
            }

            Lookup lookup;

            if (request.Id.HasValue)
            {
                // Update existing lookup
                lookup = await _lookupRepository.GetByIdAsync(request.Id.Value, cancellationToken);
                if (lookup == null)
                {
                    return Result<Guid>.Failure($"Lookup with ID '{request.Id}' not found.");
                }

                // Update properties
                lookup.Value = request.Value;
                lookup.IsDefault = request.IsDefault;
                lookup.Value1 = request.Value1;
                lookup.Value2 = request.Value2;
                lookup.ShowSequence = request.ShowSequence;
                lookup.IsActive = request.IsActive;
                lookup.ModifiedAt = DateTime.UtcNow;

                await _lookupRepository.UpdateAsync(lookup, cancellationToken);
                _logger.LogInformation("Updated lookup with ID: {LookupId}", lookup.Id);
            }
            else
            {
                // Create new lookup
                lookup = new Lookup
                {
                    Id = Guid.NewGuid(),
                    ContextId = request.ContextId,
                    Value = request.Value,
                    IsDefault = request.IsDefault,
                    Value1 = request.Value1,
                    Value2 = request.Value2,
                    ShowSequence = request.ShowSequence,
                    IsActive = request.IsActive,
                    IsDeleted = false,
                    CreatedAt = DateTime.UtcNow
                };

                await _lookupRepository.AddAsync(lookup, cancellationToken);
                _logger.LogInformation("Created new lookup with ID: {LookupId}", lookup.Id);
            }

            // If this lookup is set as default, ensure no other lookup in the same context is default
            if (request.IsDefault)
            {
                var spec = new LookupsByContextIdSpec(request.ContextId, includeInactive: true);
                var allLookups = await _lookupRepository.ListAsync(spec, cancellationToken);
                
                foreach (var otherLookup in allLookups.Where(l => l.Id != lookup.Id && l.IsDefault))
                {
                    otherLookup.IsDefault = false;
                    otherLookup.ModifiedAt = DateTime.UtcNow;
                    await _lookupRepository.UpdateAsync(otherLookup, cancellationToken);
                }
            }

            return Result<Guid>.Success(lookup.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error upserting lookup");
            return Result<Guid>.Failure("An error occurred while upserting the lookup.");
        }
    }
}
