// components/ThisPhone.tsx
import React, { useState, useRef, useMemo } from 'react';
import { Phone, ChevronDown, Check, X } from 'lucide-react';
import countries from 'world-countries';

interface Country {
  name: string;
  code: string;
  dialCode: string;
  flag: string;
  format?: string;
  minLength?: number;
  maxLength?: number;
}

interface ThisPhoneProps {
  id: string;
  label: string;
  value: string;
  onChange: (value: string) => void;
  onValidation?: (errors: string[]) => void;
  onCountryChange?: (country: Country) => void;
  disabled?: boolean;
  readOnly?: boolean;
  helpText?: string;
  placeholder?: string;
  required?: boolean;
  defaultCountry?: string;
  allowedCountries?: string[];
  blockedCountries?: string[];
  showFlag?: boolean;
  showIcon?: boolean;
  showValidationIcon?: boolean;
  validateOnBlur?: boolean;
  allowInternational?: boolean;
  requireCountryCode?: boolean;
  minLength?: number;
  maxLength?: number;
  allowExtensions?: boolean;
  customValidation?: (value: string, country?: Country) => string | null;
}

interface ValidationRule {
  test: (value: string, country?: Country) => boolean;
  message: string;
}

const ThisPhone: React.FC<ThisPhoneProps> = ({
  id,
  label,
  value,
  onChange,
  onValidation,
  onCountryChange,
  disabled = false,
  readOnly = false,
  helpText,
  placeholder = 'Enter phone number...',
  required = false,
  defaultCountry = 'US',
  allowedCountries,
  blockedCountries,
  showFlag = true,
  showIcon = true,
  showValidationIcon = true,
  validateOnBlur = true,
  allowInternational = true,
  requireCountryCode = true,
  minLength = 7,
  maxLength = 15,
  allowExtensions = false,
  customValidation
}) => {
  const [errors, setErrors] = useState<string[]>([]);
  const [isFocused, setIsFocused] = useState(false);
  const [isValidated, setIsValidated] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Process countries data
  const processedCountries: Country[] = useMemo(() => {
    return countries
      .filter(country => country.idd?.root && country.idd?.suffixes?.length)
      .map(country => {
        const dialCode = country.idd.root + (country.idd.suffixes?.[0] || '');
        return {
          name: country.name.common,
          code: country.cca2,
          dialCode,
          flag: country.flag,
          format: getPhoneFormat(country.cca2),
          minLength: getMinLength(country.cca2),
          maxLength: getMaxLength(country.cca2)
        };
      })
      .filter(country => {
        if (allowedCountries && allowedCountries.length > 0) {
          return allowedCountries.includes(country.code);
        }
        if (blockedCountries && blockedCountries.length > 0) {
          return !blockedCountries.includes(country.code);
        }
        return true;
      })
      .sort((a, b) => a.name.localeCompare(b.name));
  }, [allowedCountries, blockedCountries]);

  // Get current country
  const getCurrentCountry = (): Country | undefined => {
    if (value.startsWith('+')) {
      // Find country by dial code in the value
      const phoneWithCode = value.split(' ')[0];
      return processedCountries.find(country =>
        phoneWithCode.startsWith(country.dialCode)
      );
    }
    return processedCountries.find(country => country.code === defaultCountry);
  };

  const [selectedCountry, setSelectedCountry] = useState<Country | undefined>(
    getCurrentCountry() || processedCountries.find(c => c.code === defaultCountry)
  );

  // Helper functions for phone formats and lengths
  function getPhoneFormat(countryCode: string): string {
    const formats: Record<string, string> = {
      'US': '(XXX) XXX-XXXX',
      'GB': 'XXXX XXX XXXX',
      'DE': 'XXX XXXXXXXX',
      'FR': 'XX XX XX XX XX',
      'IN': 'XXXXX XXXXX',
      'CN': 'XXX XXXX XXXX',
      'JP': 'XXX-XXXX-XXXX',
      'AU': 'XXXX XXX XXX',
      'CA': '(XXX) XXX-XXXX',
      'BR': '(XX) XXXXX-XXXX'
    };
    return formats[countryCode] || 'XXX XXX XXXX';
  }

  function getMinLength(countryCode: string): number {
    const lengths: Record<string, number> = {
      'US': 10, 'CA': 10, 'GB': 10, 'DE': 11, 'FR': 10,
      'IN': 10, 'CN': 11, 'JP': 10, 'AU': 9, 'BR': 11
    };
    return lengths[countryCode] || 7;
  }

  function getMaxLength(countryCode: string): number {
    const lengths: Record<string, number> = {
      'US': 10, 'CA': 10, 'GB': 11, 'DE': 12, 'FR': 10,
      'IN': 10, 'CN': 11, 'JP': 11, 'AU': 10, 'BR': 11
    };
    return lengths[countryCode] || 15;
  }

  // Phone number formatting (for future use)
  // const formatPhoneNumber = (phone: string, country?: Country): string => {
  //   if (!country || !country.format) return phone;
  //
  //   const digits = phone.replace(/\D/g, '');
  //   const format = country.format;
  //   let formatted = '';
  //   let digitIndex = 0;

  //   for (let i = 0; i < format.length && digitIndex < digits.length; i++) {
  //     if (format[i] === 'X') {
  //       formatted += digits[digitIndex];
  //       digitIndex++;
  //     } else {
  //       formatted += format[i];
  //     }
  //   }

  //   return formatted;
  // };

  // Extract phone number without country code
  const extractPhoneNumber = (fullValue: string): string => {
    if (!selectedCountry) return fullValue;

    if (fullValue.startsWith(selectedCountry.dialCode)) {
      return fullValue.substring(selectedCountry.dialCode.length).trim();
    }

    if (fullValue.startsWith('+')) {
      const parts = fullValue.split(' ');
      return parts.slice(1).join(' ');
    }

    return fullValue;
  };

  // Validation rules in priority order
  const getValidationRules = (): ValidationRule[] => {
    const rules: ValidationRule[] = [];

    // 1. Required validation (highest priority)
    if (required) {
      rules.push({
        test: (val) => val.trim().length > 0,
        message: `${label} is required`
      });
    }

    // 2. Country code validation
    if (requireCountryCode) {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          return val.startsWith('+') || selectedCountry !== undefined;
        },
        message: `${label} must include a country code`
      });
    }

    // 3. Phone number format validation
    rules.push({
      test: (val, country) => {
        if (val.trim() === '') return !required;
        const phoneNumber = extractPhoneNumber(val);
        const digitsOnly = phoneNumber.replace(/\D/g, '');

        // Basic phone number validation
        if (digitsOnly.length === 0) return false;

        // Country-specific length validation
        if (country) {
          const minLen = country.minLength || minLength;
          const maxLen = country.maxLength || maxLength;
          return digitsOnly.length >= minLen && digitsOnly.length <= maxLen;
        }

        return digitsOnly.length >= minLength && digitsOnly.length <= maxLength;
      },
      message: `${label} must be a valid phone number`
    });

    // 4. Extension validation
    if (!allowExtensions) {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          return !val.toLowerCase().includes('ext') && !val.includes('#');
        },
        message: `${label} cannot contain extensions`
      });
    }

    // 5. International format validation
    if (!allowInternational && requireCountryCode) {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          return selectedCountry?.code === defaultCountry;
        },
        message: `${label} must be a domestic number`
      });
    }

    // 6. Custom validation
    if (customValidation) {
      rules.push({
        test: (val, country) => {
          const customError = customValidation(val, country);
          return customError === null;
        },
        message: customValidation(value, selectedCountry) || ''
      });
    }

    return rules;
  };

  const validateValue = (val: string): string[] => {
    const rules = getValidationRules();

    // Return only the first error found (most important)
    for (const rule of rules) {
      if (!rule.test(val, selectedCountry)) {
        return [rule.message];
      }
    }

    return [];
  };

  const handleCountrySelect = (country: Country) => {
    setSelectedCountry(country);
    setIsDropdownOpen(false);
    setSearchTerm('');

    // Update phone number with new country code
    const phoneNumber = extractPhoneNumber(value);
    const newValue = phoneNumber ? `${country.dialCode} ${phoneNumber}` : country.dialCode + ' ';
    onChange(newValue);
    onCountryChange?.(country);

    // Validate with new country
    const newErrors = validateValue(newValue);
    setErrors(newErrors);
    onValidation?.(newErrors);
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled || readOnly) return;

    let newValue = e.target.value;

    // If no country selected and value starts with +, try to detect country
    if (!selectedCountry && newValue.startsWith('+')) {
      const detectedCountry = processedCountries.find(country =>
        newValue.startsWith(country.dialCode)
      );
      if (detectedCountry) {
        setSelectedCountry(detectedCountry);
        onCountryChange?.(detectedCountry);
      }
    }

    // Ensure country code is included if required
    if (selectedCountry && requireCountryCode && !newValue.startsWith(selectedCountry.dialCode)) {
      if (newValue.startsWith('+')) {
        // User is typing a different country code
        newValue = newValue;
      } else {
        // Add country code
        newValue = selectedCountry.dialCode + ' ' + newValue;
      }
    }

    onChange(newValue);

    // Real-time validation (only if not validating on blur)
    if (!validateOnBlur) {
      const newErrors = validateValue(newValue);
      setErrors(newErrors);
      setIsValidated(newValue.trim().length > 0);
      onValidation?.(newErrors);
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);

    // Validate on blur if enabled
    if (validateOnBlur) {
      const newErrors = validateValue(value);
      setErrors(newErrors);
      setIsValidated(value.trim().length > 0);
      onValidation?.(newErrors);
    }
  };

  // Filter countries based on search
  const filteredCountries = processedCountries.filter(country =>
    country.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    country.dialCode.includes(searchTerm) ||
    country.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getValidationIcon = () => {
    if (!showValidationIcon || !isValidated || value.trim().length === 0) {
      return null;
    }

    const hasErrors = errors.length > 0;
    return hasErrors ? (
      <X size={16} className="validation-icon error" />
    ) : (
      <Check size={16} className="validation-icon success" />
    );
  };

  const hasErrors = errors.length > 0;
  const isValid = isValidated && !hasErrors && value.trim().length > 0;

  return (
    <div className="text-input-container">
      {/* Label */}
      <label className="text-input-label" htmlFor={id}>
        {label}
        {required && <span className="required-indicator">*</span>}
        {helpText && (
          <span
            className="text-input-info-icon"
            data-tooltip={helpText}
            aria-label={helpText}
          />
        )}
      </label>

      {/* Input */}
      <div className="text-input-wrapper">
        <div className={`phone-input-container ${hasErrors ? 'has-error' : ''} ${isValid ? 'is-valid' : ''} ${disabled ? 'disabled' : ''} ${isFocused ? 'focused' : ''}`}>
          {/* Country Dropdown */}
          <div className="phone-country-dropdown">
            <button
              type="button"
              className="country-selector"
              onClick={() => !disabled && !readOnly && setIsDropdownOpen(!isDropdownOpen)}
              disabled={disabled || readOnly}
              aria-label="Select country"
            >
              {selectedCountry && (
                <>
                  {showFlag && <span className="country-flag">{selectedCountry.flag}</span>}
                  <span className="country-code">{selectedCountry.dialCode}</span>
                </>
              )}
              <ChevronDown size={14} className={`dropdown-icon ${isDropdownOpen ? 'open' : ''}`} />
            </button>

            {/* Dropdown Menu */}
            {isDropdownOpen && (
              <div className="country-dropdown-menu" ref={dropdownRef}>
                <div className="country-search">
                  <input
                    type="text"
                    placeholder="Search countries..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="country-search-input"
                  />
                </div>
                <div className="country-list">
                  {filteredCountries.map((country) => (
                    <button
                      key={country.code}
                      type="button"
                      className={`country-option ${selectedCountry?.code === country.code ? 'selected' : ''}`}
                      onClick={() => handleCountrySelect(country)}
                    >
                      {showFlag && <span className="country-flag">{country.flag}</span>}
                      <span className="country-name">{country.name}</span>
                      <span className="country-dial-code">{country.dialCode}</span>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Phone Input */}
          <input
            ref={inputRef}
            id={id}
            type="tel"
            value={value}
            onChange={handlePhoneChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            disabled={disabled}
            readOnly={readOnly}
            placeholder={placeholder}
            className="phone-input"
            aria-describedby={hasErrors ? `${id}-error` : undefined}
            aria-invalid={hasErrors}
            autoComplete="tel"
          />

          {showIcon && (
            <div className="phone-icon">
              <Phone size={16} />
            </div>
          )}

          {getValidationIcon()}
        </div>

        {/* Helper Text */}
        {!hasErrors && selectedCountry && (
          <div className="phone-helper">
            <span className="helper-text">
              Format: {selectedCountry.format} ({selectedCountry.name})
            </span>
            {allowExtensions && (
              <span className="helper-text">
                Extensions allowed (use "ext" or "#")
              </span>
            )}
            {!allowInternational && (
              <span className="helper-text">
                Domestic numbers only
              </span>
            )}
          </div>
        )}

        {/* Error Message */}
        {hasErrors && (
          <div className="text-input-errors" role="alert" id={`${id}-error`}>
            <p className="error-message">
              {errors[0]}
            </p>
          </div>
        )}
      </div>

      {/* Dropdown Backdrop */}
      {isDropdownOpen && (
        <div
          className="dropdown-backdrop"
          onClick={() => setIsDropdownOpen(false)}
        />
      )}
    </div>
  );
};

export default ThisPhone;
