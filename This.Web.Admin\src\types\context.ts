// Context and TenantContext types for lookup functionality

// Context DTOs (matches backend API response)
export interface ContextDto {
  id: string;
  name: string;
  description?: string;
  category?: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  createdBy?: string;
  modifiedAt?: string;
  modifiedBy?: string;
}

export interface TenantContextDto {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  category?: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  createdBy?: string;
  modifiedAt?: string;
  modifiedBy?: string;
}

// Lookup DTOs (matches backend API response)
export interface LookupDto {
  id: string;
  contextId: string;
  value: string;
  isDefault: boolean;
  value1?: string;
  value2?: string;
  showSequence: number;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  createdBy?: string;
  modifiedAt?: string;
  modifiedBy?: string;
}

export interface TenantLookupDto {
  id: string;
  tenantId: string;
  tenantContextId: string;
  value: string;
  isDefault: boolean;
  value1?: string;
  value2?: string;
  showSequence: number;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  createdBy?: string;
  modifiedAt?: string;
  modifiedBy?: string;
}

// API Response DTOs (matches backend API response)
export interface ContextWithLookupsDto {
  context: ContextDto;
  lookups: LookupDto[];
  lookupsCount: number;
}

export interface TenantContextWithLookupsDto {
  tenantContext: TenantContextDto;
  tenantLookups: TenantLookupDto[];
  tenantLookupsCount: number;
}

// API Response wrapper (matches Result<T> from backend)
export interface ContextApiResponse {
  succeeded: boolean;
  data: ContextWithLookupsDto;
  message: string | null;
  statusCode?: number;
}

export interface TenantContextApiResponse {
  succeeded: boolean;
  data: TenantContextWithLookupsDto;
  message: string | null;
  statusCode?: number;
}

// Unified lookup option for UI components
export interface LookupOption {
  id: string;
  value: string;
  label: string;
  isDefault: boolean;
  value1?: string;
  value2?: string;
  showSequence: number;
  source: 'context' | 'tenantContext'; // To track the source
  contextId?: string; // For context lookups
  tenantContextId?: string; // For tenant context lookups
}

// Lookup service configuration
export interface LookupConfig {
  contextId?: string;
  tenantContextId?: string;
  includeInactiveLookups?: boolean;
}
