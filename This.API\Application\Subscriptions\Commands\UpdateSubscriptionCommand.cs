using Application.Subscriptions.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Subscriptions.Commands;

/// <summary>
/// Update Subscription command
/// </summary>
public class UpdateSubscriptionCommand : IRequest<Result<SubscriptionDto>>
{
    /// <summary>
    /// Subscription ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Product ID this subscription is for
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Subscription type
    /// </summary>
    public string SubscriptionType { get; set; } = "standard";

    /// <summary>
    /// Subscription status
    /// </summary>
    public string Status { get; set; } = "active";

    /// <summary>
    /// Subscription start date
    /// </summary>
    public DateTime StartDate { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Subscription end date
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Whether auto-renewal is enabled
    /// </summary>
    public bool AutoRenew { get; set; } = true;

    /// <summary>
    /// Pricing tier
    /// </summary>
    public string? PricingTier { get; set; }

    /// <summary>
    /// Template version
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// Template JSON content
    /// </summary>
    public string TemplateJson { get; set; } = "{}";

    /// <summary>
    /// Template Ids with their enabled/disabled status
    /// </summary>
    public Dictionary<Guid, bool>? TemplateDetails { get; set; }

    /// <summary>
    /// Whether the subscription is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
