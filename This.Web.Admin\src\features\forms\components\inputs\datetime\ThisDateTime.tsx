// components/ThisDateTime.tsx
import React, { useState, useRef } from 'react';
import { Calendar, Clock } from 'lucide-react';

interface ThisDateTimeProps {
  id: string;
  label: string;
  value: string;
  onChange: (value: string) => void;
  onValidation?: (errors: string[]) => void;
  disabled?: boolean;
  readOnly?: boolean;
  helpText?: string;
  placeholder?: string;
  required?: boolean;
  type?: 'datetime-local' | 'date' | 'time' | 'month' | 'year' | 'month-year';
  min?: string;
  max?: string;
  showIcon?: boolean;
  format?: 'ISO' | 'US' | 'EU';
  timeFormat?: '12h' | '24h';
  allowPast?: boolean;
  allowFuture?: boolean;
  allowWeekends?: boolean;
  allowedDays?: number[];
  businessHoursOnly?: boolean;
  startHour?: number;
  endHour?: number;
  minuteStep?: number;
  timezone?: string;
  customValidation?: (value: string) => string | null;
}

interface ValidationRule {
  test: (value: string) => boolean;
  message: string;
}

const ThisDateTime: React.FC<ThisDateTimeProps> = ({
  id,
  label,
  value,
  onChange,
  onValidation,
  disabled = false,
  readOnly = false,
  helpText,
  placeholder,
  required = false,
  type = 'datetime-local',
  min,
  max,
  showIcon = true,
  format = 'ISO',
  timeFormat = '24h',
  allowPast = true,
  allowFuture = true,
  allowWeekends = true,
  allowedDays,
  businessHoursOnly = false,
  startHour = 9,
  endHour = 17,
  minuteStep = 1,
  timezone,
  customValidation
}) => {
  const [errors, setErrors] = useState<string[]>([]);
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Helper functions
  const parseDateTime = (dateTimeString: string): Date | null => {
    if (!dateTimeString) return null;
    const date = new Date(dateTimeString);
    return isNaN(date.getTime()) ? null : date;
  };

  const parseTime = (timeString: string): Date | null => {
    if (!timeString) return null;
    // For time inputs, create a date with today's date and the specified time
    const today = new Date();
    const [hours, minutes] = timeString.split(':').map(Number);
    if (isNaN(hours) || isNaN(minutes)) return null;

    const date = new Date(today.getFullYear(), today.getMonth(), today.getDate(), hours, minutes);
    return isNaN(date.getTime()) ? null : date;
  };

  const parseYear = (yearString: string): Date | null => {
    if (!yearString) return null;
    const year = parseInt(yearString, 10);
    if (isNaN(year)) return null;

    const date = new Date(year, 0, 1); // January 1st of the specified year
    return isNaN(date.getTime()) ? null : date;
  };

  const parseValue = (valueString: string): Date | null => {
    if (!valueString) return null;

    switch (type) {
      case 'time':
        return parseTime(valueString);
      case 'year':
        return parseYear(valueString);
      case 'month':
      case 'month-year':
        // For month inputs, the value is in YYYY-MM format
        const date = new Date(valueString + '-01'); // Add day to make it a valid date
        return isNaN(date.getTime()) ? null : date;
      default:
        return parseDateTime(valueString);
    }
  };

  const formatDateTimeForDisplay = (dateTimeString: string): string => {
    if (!dateTimeString) return '';
    const date = parseDateTime(dateTimeString);
    if (!date) return dateTimeString;

    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: format === 'US'
    };

    switch (format) {
      case 'US':
        return date.toLocaleString('en-US', options);
      case 'EU':
        return date.toLocaleString('en-GB', options);
      default:
        return dateTimeString;
    }
  };

  const isWeekend = (date: Date): boolean => {
    const day = date.getDay();
    return day === 0 || day === 6; // Sunday = 0, Saturday = 6
  };

  const isBusinessHours = (date: Date): boolean => {
    const hour = date.getHours();
    return hour >= startHour && hour < endHour;
  };

  const isBusinessDay = (date: Date): boolean => {
    return !isWeekend(date);
  };

  // Validation rules in priority order
  const getValidationRules = (): ValidationRule[] => {
    const rules: ValidationRule[] = [];

    // 1. Required validation (highest priority)
    if (required) {
      rules.push({
        test: (val) => val.trim().length > 0,
        message: `${label} is required`
      });
    }

    // 2. Valid value validation (datetime/date/time/year based on type)
    rules.push({
      test: (val) => {
        if (val.trim() === '') return !required;
        const parsedValue = parseValue(val);
        return parsedValue !== null;
      },
      message: `${label} must be a valid ${type === 'time' ? 'time' : type === 'year' ? 'year' : type === 'month' || type === 'month-year' ? 'month' : type === 'date' ? 'date' : 'date and time'}`
    });

    // 3. Minimum value validation
    if (min) {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const parsedValue = parseValue(val);
          const minValue = parseValue(min);
          return !parsedValue || !minValue || parsedValue >= minValue;
        },
        message: `${label} must be on or after ${type === 'time' ? min : formatDateTimeForDisplay(min)}`
      });
    }

    // 4. Maximum value validation
    if (max) {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const parsedValue = parseValue(val);
          const maxValue = parseValue(max);
          return !parsedValue || !maxValue || parsedValue <= maxValue;
        },
        message: `${label} must be on or before ${type === 'time' ? max : formatDateTimeForDisplay(max)}`
      });
    }

    // 5. Past validation (skip for time-only inputs)
    if (!allowPast && type !== 'time') {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const parsedValue = parseValue(val);
          const now = new Date();
          return !parsedValue || parsedValue >= now;
        },
        message: `${label} cannot be in the past`
      });
    }

    // 6. Future validation (skip for time-only inputs)
    if (!allowFuture && type !== 'time') {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const parsedValue = parseValue(val);
          const now = new Date();
          return !parsedValue || parsedValue <= now;
        },
        message: `${label} cannot be in the future`
      });
    }

    // 7. Weekend validation (skip for time-only inputs)
    if (!allowWeekends && type !== 'time' && type !== 'year') {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const parsedValue = parseValue(val);
          return !parsedValue || !isWeekend(parsedValue);
        },
        message: `${label} cannot be on a weekend`
      });
    }

    // 8. Business hours validation (skip for time-only inputs)
    if (businessHoursOnly && type !== 'time' && type !== 'year') {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const parsedValue = parseValue(val);
          return !parsedValue || (isBusinessDay(parsedValue) && isBusinessHours(parsedValue));
        },
        message: `${label} must be during business hours (${startHour}:00 - ${endHour}:00, Monday-Friday)`
      });
    }

    // 9. Allowed days validation (skip for time-only inputs)
    if (allowedDays && allowedDays.length > 0 && type !== 'time' && type !== 'year') {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const parsedValue = parseValue(val);
          return !parsedValue || allowedDays.includes(parsedValue.getDay());
        },
        message: `${label} must be on an allowed day of the week`
      });
    }

    // 10. Minute step validation (for time and datetime inputs)
    if (minuteStep > 1 && (type === 'time' || type === 'datetime-local')) {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const parsedValue = parseValue(val);
          return !parsedValue || parsedValue.getMinutes() % minuteStep === 0;
        },
        message: `${label} minutes must be in increments of ${minuteStep}`
      });
    }

    // 11. Custom validation
    if (customValidation) {
      rules.push({
        test: (val) => {
          const customError = customValidation(val);
          return customError === null;
        },
        message: customValidation(value) || ''
      });
    }

    return rules;
  };

  const validateValue = (val: string): string[] => {
    const rules = getValidationRules();

    // Return only the first error found (most important)
    for (const rule of rules) {
      if (!rule.test(val)) {
        return [rule.message];
      }
    }

    return [];
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled || readOnly) return;

    const newValue = e.target.value;
    onChange(newValue);

    // Real-time validation
    const newErrors = validateValue(newValue);
    setErrors(newErrors);
    onValidation?.(newErrors);
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const getInputType = (): string => {
    switch (type) {
      case 'year':
        return 'number';
      case 'month-year':
        return 'month';
      default:
        return type;
    }
  };

  const getPlaceholderText = (): string => {
    if (placeholder) return placeholder;

    switch (type) {
      case 'datetime-local':
        return timeFormat === '12h' ? 'Select date and time (12h)...' : 'Select date and time (24h)...';
      case 'date':
        return 'Select date...';
      case 'time':
        return timeFormat === '12h' ? 'Select time (12h)...' : 'Select time (24h)...';
      case 'month':
        return 'Select month...';
      case 'year':
        return 'Enter year...';
      case 'month-year':
        return 'Select month and year...';
      default:
        return 'Select...';
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'time':
        return <Clock size={16} />;
      case 'datetime-local':
        return (
          <>
            <Calendar size={16} />
            <Clock size={14} />
          </>
        );
      default:
        return <Calendar size={16} />;
    }
  };

  const getStepAttribute = (): number => {
    return minuteStep * 60; // Convert minutes to seconds for HTML step attribute
  };

  const hasErrors = errors.length > 0;

  return (
    <div className="text-input-container">
      {/* Label */}
      <label className="text-input-label" htmlFor={id}>
        {label}
        {required && <span className="required-indicator">*</span>}
        {helpText && (
          <span
            className="text-input-info-icon"
            data-tooltip={helpText}
            aria-label={helpText}
          />
        )}
      </label>

      {/* Input */}
      <div className="text-input-wrapper">
        <div className={`datetime-input-container ${hasErrors ? 'has-error' : ''} ${disabled ? 'disabled' : ''} ${isFocused ? 'focused' : ''}`}>
          <input
            ref={inputRef}
            id={id}
            type={getInputType()}
            value={value}
            onChange={handleChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            disabled={disabled}
            readOnly={readOnly}
            placeholder={getPlaceholderText()}
            className="datetime-input"
            aria-describedby={hasErrors ? `${id}-error` : undefined}
            aria-invalid={hasErrors}
            min={type === 'year' ? (min ? new Date(min).getFullYear().toString() : '1900') : min}
            max={type === 'year' ? (max ? new Date(max).getFullYear().toString() : '2100') : max}
            step={type === 'year' ? '1' : (type === 'datetime-local' || type === 'time' ? getStepAttribute() : undefined)}
          />

          {showIcon && (
            <div className="datetime-icon">
              {getIcon()}
            </div>
          )}
        </div>

        {/* Helper Text */}
        {!hasErrors && (
          <div className="datetime-helper">
            {(type === 'time' || type === 'datetime-local') && (
              <span className="helper-text">
                Time format: {timeFormat === '12h' ? '12-hour (AM/PM)' : '24-hour'}
              </span>
            )}
            {!allowPast && !allowFuture && (
              <span className="helper-text">Current time only</span>
            )}
            {!allowPast && allowFuture && (
              <span className="helper-text">Current time or future only</span>
            )}
            {allowPast && !allowFuture && (
              <span className="helper-text">Current time or past only</span>
            )}
            {businessHoursOnly && (
              <span className="helper-text">Business hours only ({startHour}:00 - {endHour}:00, Monday-Friday)</span>
            )}
            {!allowWeekends && !businessHoursOnly && (
              <span className="helper-text">Weekdays only</span>
            )}
            {minuteStep > 1 && (
              <span className="helper-text">Time in {minuteStep}-minute increments</span>
            )}
            {timezone && (
              <span className="helper-text">Timezone: {timezone}</span>
            )}
          </div>
        )}

        {/* Error Message */}
        {hasErrors && (
          <div className="text-input-errors" role="alert" id={`${id}-error`}>
            <p className="error-message">
              {errors[0]}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ThisDateTime;
