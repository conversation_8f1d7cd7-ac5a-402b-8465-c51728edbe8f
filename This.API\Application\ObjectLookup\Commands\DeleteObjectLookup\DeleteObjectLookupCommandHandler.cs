using Domain.Entities;
using Abstraction.Database.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.ObjectLookup.Commands.DeleteObjectLookup;

/// <summary>
/// Handler for DeleteObjectLookupCommand
/// </summary>
public class DeleteObjectLookupCommandHandler : IRequestHandler<DeleteObjectLookupCommand, Result<bool>>
{
    private readonly IRepository<Domain.Entities.ObjectLookup> _objectLookupRepository;
    private readonly ILogger<DeleteObjectLookupCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteObjectLookupCommandHandler(
        IRepository<Domain.Entities.ObjectLookup> objectLookupRepository,
        ILogger<DeleteObjectLookupCommandHandler> logger)
    {
        _objectLookupRepository = objectLookupRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<bool>> Handle(DeleteObjectLookupCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Get existing ObjectLookup
            var objectLookup = await _objectLookupRepository.GetByIdAsync(request.Id, cancellationToken);
            if (objectLookup == null)
            {
                return Result<bool>.Failure($"ObjectLookup with ID '{request.Id}' not found.");
            }

            // Soft delete the ObjectLookup
            await _objectLookupRepository.DeleteAsync(objectLookup, cancellationToken);
            
            _logger.LogInformation("Deleted ObjectLookup with ID: {ObjectLookupId}", request.Id);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting ObjectLookup with ID: {ObjectLookupId}", request.Id);
            return Result<bool>.Failure("An error occurred while deleting the ObjectLookup.");
        }
    }
}
