import React from 'react';
import { ChevronRight, Home } from 'lucide-react';
import { cn } from '@/shared/utils/utils';

export interface BreadcrumbItem {
  label: string;
  href?: string;
  onClick?: () => void;
  isActive?: boolean;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
  showHomeIcon?: boolean;
  separator?: React.ReactNode;
  isLoading?: boolean;
}

export const Breadcrumb: React.FC<BreadcrumbProps> = ({
  items,
  className,
  showHomeIcon = false,
  separator = <ChevronRight className="w-4 h-4 text-muted-foreground" />,
  isLoading = false
}) => {
  if (isLoading) {
    return (
      <nav className={cn('flex items-center space-x-2', className)} aria-label="Breadcrumb">
        <div className="flex items-center space-x-2">
          <div className="h-4 w-20 bg-muted animate-pulse rounded"></div>
          <ChevronRight className="w-4 h-4 text-muted-foreground" />
          <div className="h-4 w-16 bg-muted animate-pulse rounded"></div>
          <ChevronRight className="w-4 h-4 text-muted-foreground" />
          <div className="h-4 w-12 bg-muted animate-pulse rounded"></div>
        </div>
      </nav>
    );
  }

  if (!items || items.length === 0) {
    return null;
  }

  return (
    <nav className={cn('flex items-center', className)} aria-label="Breadcrumb">
      <div className="flex items-center min-w-0 max-w-full overflow-hidden">
        {showHomeIcon && (
          <div className="flex items-center flex-shrink-0">
            <Home className="w-4 h-4 text-muted-foreground" />
            {items.length > 0 && <span className="mx-2">{separator}</span>}
          </div>
        )}

        {items.length > 0 && (
          <div className="flex items-center min-w-0 overflow-hidden">
            {/* Always show first item if there are multiple items */}
            {items.length > 1 && (
              <>
                <div className="flex items-center flex-shrink-0">
                  {items[0].onClick ? (
                    <button
                      type="button"
                      onClick={items[0].onClick}
                      className="text-sm font-normal text-muted-foreground hover:text-primary focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 focus:outline-none rounded-sm px-1 py-0.5 transition-colors breadcrumb-button"
                      title={items[0].label}
                    >
                      <span className="truncate max-w-[120px] block">{items[0].label}</span>
                    </button>
                  ) : (
                    <span className="text-sm font-normal text-muted-foreground" title={items[0].label}>
                      <span className="truncate max-w-[120px] block">{items[0].label}</span>
                    </span>
                  )}
                  <span className="mx-2 flex-shrink-0" aria-hidden="true">{separator}</span>
                </div>

                {/* Show ellipsis if there are more than 2 items */}
                {items.length > 2 && (
                  <div key="breadcrumb-ellipsis" className="flex items-center flex-shrink-0">
                    <span className="text-sm text-muted-foreground px-1">...</span>
                    <span className="mx-2 flex-shrink-0" aria-hidden="true">{separator}</span>
                  </div>
                )}
              </>
            )}

            {/* Always show the last item */}
            <div key={`breadcrumb-${items[items.length - 1].label}`} className="flex items-center min-w-0 flex-1">
              {items[items.length - 1].onClick ? (
                <button
                  type="button"
                  onClick={items[items.length - 1].onClick}
                  className="text-sm font-medium text-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 focus:outline-none rounded-sm px-1 py-0.5 transition-colors min-w-0 breadcrumb-button"
                  aria-current="page"
                  title={items[items.length - 1].label}
                >
                  <span className="truncate block">{items[items.length - 1].label}</span>
                </button>
              ) : (
                <span
                  className="text-sm font-medium text-foreground min-w-0"
                  aria-current="page"
                  title={items[items.length - 1].label}
                >
                  <span className="truncate block">{items[items.length - 1].label}</span>
                </span>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Breadcrumb;
